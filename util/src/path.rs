#[allow(deprecated)]
use std::env::home_dir;
use std::path::PathBuf;

use tauri::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager as _};

// get app log dir
pub fn app_log_dir(app: &App) -> PathBuf {
    app_log_dir_inner(app.handle())
}

pub fn app_log_dir_inner(app: &AppHandle) -> PathBuf {
    let path = app
        .path()
        .app_log_dir()
        .unwrap_or(app_data_dir_inner(app, &["logs"]));

    // create log dir
    if !path.exists() {
        log::info!("init app log dir {}", path.display());
        let _ = std::fs::create_dir_all(&path);
    }

    path
}

pub fn app_data_dir(app: &App, dir: &[&str]) -> PathBuf {
    app_data_dir_inner(app.handle(), dir)
}

pub fn app_data_dir_inner(app: &AppHandle, dir: &[&str]) -> PathBuf {
    let app_data_dir = app.path().app_data_dir().unwrap_or(
        #[allow(deprecated)]
        home_dir()
            .unwrap()
            .join(".atominnolab")
            .join(&app.config().identifier),
    );

    if dir.is_empty() {
        return app_data_dir;
    }

    let sub_dir = dir.iter().collect::<PathBuf>();
    app_data_dir.join(sub_dir)
}

pub fn tmp_dir(app: &App) -> PathBuf {
    tmp_dir_inner(app.handle())
}

pub fn tmp_dir_inner(app: &AppHandle) -> PathBuf {
    let app_path = app.path();

    // fallback to Download -> ~/.atominnolab/{identifier}
    let fallback_dir = app_path.download_dir().unwrap_or(
        #[allow(deprecated)]
        home_dir()
            .unwrap()
            .join(".atominnolab")
            .join(&app.config().identifier),
    );
    app_path.temp_dir().unwrap_or(fallback_dir)
}
