use std::{
    fs::{self, File, OpenOptions},
    io::Read,
    path::PathBuf,
};

use tauri::{App, AppHandle};

use crate::path::*;

// clean old log
pub fn clean_old_log(app: &App, day: i64) {
    let log_path = app_log_dir(app);
    if !log_path.exists() {
        log::info!("log path is not exits yet, will not clean");
        return;
    }

    let now = chrono::Local::now().date_naive();
    let max_age = chrono::Duration::days(day);

    for entry in walkdir::WalkDir::new(log_path)
        .max_depth(1)
        .into_iter()
        .filter_map(|p| p.ok())
    {
        let path = entry.path();
        // skip sub dir
        if path.is_dir() {
            continue;
        }

        // Handle files without extensions
        let ext = match path.extension() {
            Some(ext) if ext == "log" => continue, // Keep .log files
            Some(ext) => {
                let is_normal_log = path
                    .file_stem()
                    .is_some_and(|file_stem| file_stem.to_string_lossy().ends_with("log"));

                if !is_normal_log {
                    log::info!("Removing old log file: {}", path.display());
                    if let Err(e) = fs::remove_file(path) {
                        log::error!("Failed to remove file {}: {}", path.display(), e);
                    }
                    continue;
                }

                ext
            }
            None => {
                log::info!("Removing old log file: {}", path.display());
                if let Err(e) = fs::remove_file(path) {
                    log::error!("Failed to remove file {}: {}", path.display(), e);
                }
                continue;
            }
        };

        // Parse date from filename
        match chrono::NaiveDate::parse_from_str(ext.to_string_lossy().as_ref(), "%Y-%m-%d") {
            Ok(log_date) if now.signed_duration_since(log_date) > max_age => {
                log::info!("Removing old log file: {}", path.display());
                if let Err(e) = fs::remove_file(path) {
                    log::error!("Failed to remove file {}: {}", path.display(), e);
                }
            }
            Ok(_) => (), // File is not old enough
            Err(_) => {
                // Invalid date format - remove the file
                log::info!("Removing old log file: {}", path.display());
                if let Err(e) = fs::remove_file(path) {
                    log::error!("Failed to remove file {}: {}", path.display(), e);
                }
            }
        }
    }
}

// export log
pub fn archive_log(app: &AppHandle) -> Result<PathBuf, String> {
    let log_path = app_log_dir_inner(app);
    let tmp_dir = tmp_dir_inner(app).join(format!("{}.zip", &app.config().identifier));

    let log_archive_file = OpenOptions::new()
        .create(true)
        .truncate(true)
        .write(true)
        .open(&tmp_dir)
        .map_err(|err| {
            log::error!("open file error {}. {}", tmp_dir.display(), err);
            err.to_string()
        })?;

    let mut zip_writer = zip::ZipWriter::new(log_archive_file);

    let options = zip::write::SimpleFileOptions::default()
        .compression_method(zip::CompressionMethod::Deflated)
        .large_file(true)
        .unix_permissions(0o755);

    for entry in walkdir::WalkDir::new(log_path)
        .into_iter()
        .filter_map(|e| e.ok())
    {
        let path = entry.path();
        if !path.is_file() {
            continue;
        }

        let file_name = path
            .file_name()
            .map(|name| name.to_string_lossy().to_string())
            .unwrap_or("default.log".to_string());

        zip_writer.start_file(file_name, options).map_err(|err| {
            log::error!("compose {} error. {}", path.display(), err);
            err.to_string()
        })?;

        let file = File::open(path).map_err(|err| {
            log::error!("open {} error. {}", path.display(), err);
            err.to_string()
        })?;

        let _ = file.sync_all();
        let file_size = file.metadata().unwrap().len();
        let mut limited_reader = file.take(file_size);

        std::io::copy(&mut limited_reader, &mut zip_writer).map_err(|err| {
            log::error!("copy {} error. {}", path.display(), err);
            err.to_string()
        })?;
    }

    Ok(tmp_dir)
}
