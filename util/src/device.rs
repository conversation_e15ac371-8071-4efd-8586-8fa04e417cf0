use std::sync::OnceLock;

pub static DEVICE_SN: OnceLock<String> = OnceLock::new();
pub static APP_VERSION: OnceLock<String> = OnceLock::new();

pub fn device_sn() -> &'static str {
    DEVICE_SN
        .get()
        .as_ref()
        .expect("Device SN should be initialized")
}

pub fn init_device_sn(device_sn: String) -> String {
    // let networks = Networks::new_with_refreshed_list();
    // let network = networks
    //     .into_iter()
    //     .filter(|(_, network)| {
    //         network.mac_address() != MacAddr::UNSPECIFIED && !network.ip_networks().is_empty()
    //     })
    //     .filter(|(name, _)| {
    //         !name.contains("vmnet") && !name.contains("utun") && !name.contains("bridge")
    //     })
    //     .collect::<Vec<_>>();

    // let mut serial_number = String::new();

    // #[cfg(target_os = "macos")]
    // {
    //     serial_number.clear();
    //     if let Ok(uuid) = get_device_id(app_handle) {
    //         serial_number.push_str(&uuid);
    //     }
    // }

    // let device_sn = if serial_number.is_empty() {
    //     network
    //         .first()
    //         .map(|(_, network)| network.mac_address().to_string())
    //         .unwrap_or_default()
    // } else {
    //     serial_number
    // };

    // let device_sn = String::new();

    DEVICE_SN.get_or_init(|| device_sn.clone());

    device_sn
}

pub fn init_app_version(app_version: &str) {
    APP_VERSION.get_or_init(|| app_version.to_string());
}

pub fn app_version() -> &'static str {
    APP_VERSION
        .get()
        .as_ref()
        .expect("App version should be initialized")
}

#[cfg(target_os = "macos")]
#[allow(dead_code)]
fn get_device_id(app_handle: &tauri::AppHandle) -> anyhow::Result<String> {
    fn extract_id(content: &str) -> anyhow::Result<String> {
        content
            .lines()
            .find(|line| line.contains("IOPlatformUUID"))
            .and_then(|line| line.split('"').nth(3))
            .map(|serial| serial.to_string())
            .ok_or_else(|| anyhow::anyhow!("Serial number not found"))
    }

    use anyhow::Context;
    use tauri_plugin_shell::ShellExt as _;

    let shell = app_handle.shell();
    let output = tauri::async_runtime::block_on(async move {
        shell
            .command("ioreg")
            .args(["-rd1", "-c", "IOPlatformExpertDevice"])
            .output()
            .await
            .context("exec ioreg command on macos error")
    })?;

    let output_str = String::from_utf8_lossy(&output.stdout);

    extract_id(&output_str)
}
