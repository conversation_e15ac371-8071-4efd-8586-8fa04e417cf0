use std::sync::OnceLock;

use chrono::{DateTime, FixedOffset};
use serde::{Deserialize, Serialize};

use crate::device::app_version;

const DEFAULT_KEY: &str = "default_key";

pub trait EventTrait {
    // event name
    fn event_name(&self) -> &str;
    // event properties
    fn event_props(&self) -> Option<serde_json::Value>;
}

pub trait TelemetrySend {
    async fn send(&self, device_id: &str) -> anyhow::Result<serde_json::Value>;
}

pub enum WisFileEvent {
    AppOpen,
    FirstOpen,
    FileRename {
        count: usize,
    },
    FileClassify {
        count: usize,
        class_count: usize,
    },
    UiEvent {
        event: String,
        props: serde_json::Value,
    },
}

pub static CHANNEL: OnceLock<Option<String>> = OnceLock::new();
pub static GROUP: OnceLock<Option<String>> = OnceLock::new();

pub fn init_channel(channel: Option<String>) {
    CHANNEL.set(channel).unwrap();
}

pub fn init_group(group: Option<String>) {
    GROUP.set(group).unwrap();
}

pub fn channel() -> Option<String> {
    CHANNEL.get().cloned().flatten()
}

pub fn group() -> Option<String> {
    GROUP.get().cloned().flatten()
}

impl EventTrait for WisFileEvent {
    fn event_name(&self) -> &str {
        match self {
            WisFileEvent::AppOpen => "app_open",
            WisFileEvent::FirstOpen => "first-open",
            WisFileEvent::FileRename { .. } => "file_rename",
            WisFileEvent::FileClassify { .. } => "file_classify",
            WisFileEvent::UiEvent { event, .. } => event,
        }
    }

    fn event_props(&self) -> Option<serde_json::Value> {
        match self {
            WisFileEvent::AppOpen => None,
            WisFileEvent::FirstOpen => None,
            WisFileEvent::FileRename { count } => Some(serde_json::json!({
                "count": count
            })),
            WisFileEvent::FileClassify { count, class_count } => Some(serde_json::json!({
                "count": count,
                "class_count": class_count
            })),
            WisFileEvent::UiEvent { event: _, props } => Some(props.clone()),
        }
    }
}

impl<T> From<&T> for EventRequest
where
    T: EventTrait,
{
    fn from(event: &T) -> Self {
        let mut request = EventRequest::default();

        let app_version = app_version();

        request.event_name.push_str(event.event_name());
        let props = match event.event_props().unwrap_or(serde_json::json!({})) {
            serde_json::Value::Null => {
                serde_json::json!({"app_version": app_version })
            }
            serde_json::Value::Bool(value) => {
                serde_json::json!({"app_version": app_version, DEFAULT_KEY: value })
            }
            serde_json::Value::Number(value) => {
                serde_json::json!({"app_version": app_version, DEFAULT_KEY: value })
            }
            serde_json::Value::String(value) => {
                serde_json::json!({"app_version": app_version, DEFAULT_KEY: value })
            }
            serde_json::Value::Array(value) => {
                serde_json::json!({"app_version": app_version, DEFAULT_KEY: value })
            }
            serde_json::Value::Object(mut map) => {
                map.insert("app_version".to_string(), app_version.into());
                serde_json::Value::Object(map)
            }
        };

        request.props = props;

        request
    }
}

#[serde_with::serde_as]
#[derive(Deserialize, Serialize, Debug)]
pub struct EventRequest {
    /// User id, optional
    pub user_id: Option<String>,
    /// Event name, required
    pub event_name: String,
    /// Event time, required
    pub time_stamp: DateTime<FixedOffset>,
    /// channel
    pub channel: String,
    /// product
    pub product: String,
    /// properties
    pub props: serde_json::Value,
    /// group
    pub group: Option<String>,
    /// experimental project
    pub exp: Option<String>,
    /// Session id, optional
    pub session_id: Option<String>,
}

pub fn now() -> DateTime<FixedOffset> {
    chrono::Utc::now().with_timezone(&FixedOffset::east_opt(0).unwrap())
}

impl Default for EventRequest {
    fn default() -> Self {
        Self {
            user_id: None,
            event_name: String::new(),
            time_stamp: now(),
            product: String::from("wisfile"),
            props: serde_json::Value::Null,
            exp: None,
            session_id: None,
            group: group(),
            channel: channel().unwrap_or_default(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_utc_time() {
        let default_time = EventRequest::default().time_stamp;

        let utc = chrono::Utc::now();
        let delta = utc.signed_duration_since(&default_time);

        assert!(delta.num_seconds() < 10);
    }

    #[test]
    fn test_default_utc_time_str() {
        let json = serde_json::to_value(&EventRequest::default()).unwrap();

        let time_str = json["time_stamp"].as_str();
        assert!(time_str.is_some());

        // RFC3339
        if let Some(time_str) = time_str {
            assert!(time_str.ends_with("Z"));
        }
    }
}
