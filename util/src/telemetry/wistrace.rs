use std::{env, path::Path};

use anyhow::Context;
use reqwest::Body;
use serde::{Deserialize, Serialize, de::DeserializeOwned};
use tauri::utils::platform::target_triple;

use crate::{
    device::device_sn,
    telemetry::{client::WISTRACE_CLIENT, constants::WISTRACE_URL_PREFIX_ENV_NAME},
};

impl TracingCollection {
    pub async fn create_collection(&self) -> anyhow::Result<String> {
        let url = format!(
            "{}/collections/new",
            env::var(WISTRACE_URL_PREFIX_ENV_NAME)
                .unwrap_or("http://wistrace-dev.atominfinite.ai/api/v1".to_string())
        );
        let response = WISTRACE_CLIENT
            .post(url)
            .body(Body::from(serde_json::to_string(&self).unwrap()))
            .send()
            .await
            .context("create collection error")?
            .json::<ApiResponse>()
            .await
            .context("deserialize failed")?;

        let collection_id = response
            .into_inner::<String>()
            .context("can not get collection id")?;

        Ok(collection_id)
    }
}
pub async fn wistrace_upload_file<P: AsRef<Path>>(path: P, collection: &str) -> anyhow::Result<()> {
    let url = format!(
        "{}/collections/{collection}/file",
        env::var(WISTRACE_URL_PREFIX_ENV_NAME)
            .unwrap_or("http://wistrace-dev.atominfinite.ai/api/v1".to_string())
    );

    let file_path = path.as_ref();

    log::info!("try to upload {}", file_path.display());
    let multipart = reqwest::multipart::Form::new()
        .file("file", file_path)
        .await
        .context("build multipart request error")?;

    let response = WISTRACE_CLIENT
        .post(&url)
        .multipart(multipart)
        .send()
        .await
        .context("create collection error")?;
    if !response.status().is_success() {
        let resp = response.text().await.ok();
        log::error!("upload file failed {resp:?}");
        anyhow::bail!("upload file failed {}", resp.unwrap_or_default());
    }

    let text = response.text().await?;
    log::info!("response: {text}");
    let response = serde_json::from_str::<ApiResponse>(&text)?;
    log::info!("response: {response:?}");

    if !response.success {
        anyhow::bail!("upload failed, response: {:?}", response);
    }

    Ok(())
}

pub async fn update_collection_email(email: &str, collection: &str) -> anyhow::Result<()> {
    let url = format!(
        "{}/collections/{collection}",
        env::var(WISTRACE_URL_PREFIX_ENV_NAME)
            .unwrap_or("http://wistrace-dev.atominfinite.ai/api/v1".to_string())
    );
    let response = WISTRACE_CLIENT
        .put(url)
        .body(Body::from(
            serde_json::json!({
                "email":email
            })
            .to_string(),
        ))
        .send()
        .await
        .context("create collection error")?
        .json::<ApiResponse>()
        .await
        .context("deserialize failed")?;

    log::info!("update {collection} email to {email}, response: {response:?}");

    Ok(())
}

#[derive(Serialize)]
pub struct TracingCollection {
    pub title: String,
    pub product_type: ProductType,
    pub issue_kind: String,
    pub app_version: String,
    pub platform: PlatformType,
    pub request_id: String,
}

#[derive(Deserialize, Debug)]
pub struct ApiResponse {
    pub success: bool,
    pub data: Option<serde_json::Value>,
    pub message: String,
    pub error: Option<ErrorDetails>,
}

#[derive(Debug, Default)]
pub struct UploadedFileResp {
    pub id: String,
    pub filename: String,
    pub original_filename: String,
    pub file_size: i64,
    pub file_type: Option<String>,
    pub md5: Option<String>,
    pub storage_path: String,
}

impl ApiResponse {
    pub fn into_inner<T: DeserializeOwned>(self) -> Option<T> {
        self.data
            .and_then(|data| serde_json::from_value::<T>(data).ok())
    }
}

#[derive(Deserialize, Debug)]
pub struct ErrorDetails {
    pub code: u16,
    pub error_msg: String,
}

#[derive(Serialize)]
pub enum ProductType {
    Wisfile,
    Wispaper,
    Wissearch,
}

#[derive(Serialize)]
pub enum PlatformType {
    MacArm,
    MacX86,
    Windows,
    Web,
    Unknown,
}

#[derive(Clone, Copy, Debug, Serialize, Deserialize)]
pub enum IssueKind {
    Rename,
    Classify,
    Both,
}

impl IssueKind {
    pub fn contains_rename(&self) -> bool {
        matches!(self, IssueKind::Rename | IssueKind::Both)
    }

    pub fn contains_classify(&self) -> bool {
        matches!(self, IssueKind::Classify | IssueKind::Both)
    }
}

impl Default for ProductType {
    fn default() -> Self {
        Self::Wisfile
    }
}

impl Default for PlatformType {
    fn default() -> Self {
        Self::new()
    }
}

impl PlatformType {
    fn new() -> Self {
        let target = target_triple().unwrap();

        if target.contains("darwin") {
            if target.contains("x86") {
                Self::MacX86
            } else {
                Self::MacArm
            }
        } else if target.contains("windows") {
            Self::Windows
        } else if target.contains("wasm") {
            Self::Web
        } else {
            Self::Unknown
        }
    }
}

impl TracingCollection {
    pub fn new(issue_kind: IssueKind, app_version: &str) -> Self {
        Self {
            issue_kind: issue_kind.as_str().to_string(),
            app_version: app_version.to_string(),
            title: format!("WisFile Collection For {}", issue_kind.as_str()),
            product_type: ProductType::default(),
            platform: PlatformType::default(),
            request_id: device_sn().to_string(),
        }
    }
}

impl IssueKind {
    pub fn as_str(&self) -> &str {
        match self {
            IssueKind::Rename => "Rename",
            IssueKind::Classify => "Classify",
            IssueKind::Both => "RenameAndClassify",
        }
    }
}
