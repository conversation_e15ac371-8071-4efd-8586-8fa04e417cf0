use std::sync::atomic::AtomicBool;

pub mod client;
pub mod event;
pub mod wistrace;

pub mod constants {
    pub const EVENT_NEW_URL: &str = "/event/new";
    pub const API_LOG_URL: &str = "/api/logv2";
    pub const TELEMETRY_TOKEN_ENV_NAME: &str = "TELEMETRY_TOKEN";
    pub const TELEMETRY_URL_PREFIX_ENV_NAME: &str = "TELEMETRY_URL_PREFIX";
    pub const SINGAPORE_TELEMETRY_URL_PREFIX_ENV_NAME: &str = "SINGAPORE_TELEMETRY_URL_PREFIX";
    pub const WISTRACE_TOKEN_ENV_NAME: &str = "WISTRACE_TOKEN";
    pub const WISTRACE_URL_PREFIX_ENV_NAME: &str = "WISTRACE_URL_PREFIX";
}

lazy_static::lazy_static! {
    pub static ref FLY_MODE: AtomicBool = AtomicBool::new(false);
}
