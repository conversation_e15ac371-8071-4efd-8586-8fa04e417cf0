use std::{
    sync::{LazyLock, atomic::Ordering},
    time::Duration,
};

use anyhow::Context;

use crate::telemetry::{
    FLY_MODE,
    constants::*,
    event::{EventRequest, EventTrait, TelemetrySend},
};

pub static TELEMETRY_CLIENT: LazyLock<reqwest::Client> = LazyLock::new(|| {
    use reqwest::header::*;

    let mut headers = HeaderMap::new();
    headers.insert(
        reqwest::header::AUTHORIZATION,
        HeaderValue::from_str(&std::env::var(TELEMETRY_TOKEN_ENV_NAME).unwrap()).unwrap(),
    );

    headers.insert(
        reqwest::header::CONTENT_TYPE,
        HeaderValue::from_static("application/json"),
    );

    reqwest::Client::builder()
        .default_headers(headers)
        .timeout(Duration::from_secs(3))
        .build()
        .unwrap()
});

pub static WISTRACE_CLIENT: LazyLock<reqwest::Client> = LazyLock::new(|| {
    use reqwest::header::*;

    let mut headers = HeaderMap::new();
    headers.insert(
        reqwest::header::AUTHORIZATION,
        HeaderValue::from_str(&std::env::var(WISTRACE_TOKEN_ENV_NAME).unwrap()).unwrap(),
    );

    headers.insert(
        reqwest::header::CONTENT_TYPE,
        HeaderValue::from_static("application/json"),
    );

    reqwest::Client::builder()
        .default_headers(headers)
        .timeout(Duration::from_secs(30))
        .build()
        .unwrap()
});

impl<T> TelemetrySend for T
where
    T: EventTrait,
{
    async fn send(&self, device_id: &str) -> anyhow::Result<serde_json::Value> {
        async fn send_inner(
            url: &str,
            request: &EventRequest,
        ) -> anyhow::Result<serde_json::Value> {
            let response = TELEMETRY_CLIENT
                .post(url)
                .body(reqwest::Body::from(serde_json::to_vec(&request).unwrap()))
                .send()
                .await
                .context("send event error")?;

            if !response.status().is_success() {
                let text = response.text().await;
                anyhow::bail!("send event {} error, resp {:?}", request.event_name, text);
            } else {
                response
                    .json::<serde_json::Value>()
                    .await
                    .map_err(|err| anyhow::anyhow!("deserialize event response error: {}", err))
            }
        }

        if FLY_MODE.load(Ordering::SeqCst) {
            log::info!("fly mode, will not send any event");
            return Ok(serde_json::json!({}));
        }

        // let url_prefix = std::env::var(TELEMETRY_URL_PREFIX_ENV_NAME)
        //     .unwrap_or("http://wisevent.dev.atominnolab.com/api/v1".into());
        // let event_send_url = format!("{url_prefix}{EVENT_NEW_URL}");

        let mut request = EventRequest::from(self);
        request.user_id = Some(device_id.to_string());

        // if let Err(err) = send_inner(&event_send_url, &request).await {
        //     log::error!("send event to {event_send_url} failed: {err}");
        // } else {
        //     log::info!(
        //         "send event {} to {}, props: {} successfully",
        //         request.event_name,
        //         event_send_url,
        //         request.props
        //     );
        // };

        let singapore_url_prefix = std::env::var(SINGAPORE_TELEMETRY_URL_PREFIX_ENV_NAME)
            .unwrap_or("http://wisevent-pub.prod.atominnolab.com/api/v1".into());
        let singapore_event_send_url = format!("{singapore_url_prefix}{API_LOG_URL}");

        match send_inner(&singapore_event_send_url, &request).await {
            Ok(result) => {
                log::info!(
                    "send event {} to {}, props: {} successfully",
                    request.event_name,
                    singapore_event_send_url,
                    request.props
                );
                Ok(result)
            }
            Err(err) => {
                log::error!("send event to {singapore_event_send_url} failed: {err}");

                Ok(serde_json::json!({}))
            }
        }

        // if let Err(err) = send_inner(&singapore_event_send_url, &request).await {
        //     log::error!("send event to {singapore_event_send_url} failed: {err}");
        // } else {
        //     log::info!(
        //         "send event {} to {}, props: {} successfully",
        //         request.event_name,
        //         singapore_event_send_url,
        //         request.props
        //     );
        // };

        // Ok(())
    }
}
