[package]
name = "util"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = "1.0.98"
chrono = "0.4.41"
log = "0.4.27"
reqwest = { version = "0.12.15", features = ["json", "multipart", "stream"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
serde_with = { version = "3.12.0", features = ["chrono"] }
sys-locale = "0.3.2"
sysinfo = { version = "0.35.1", default-features = false, features = [
  "network",
  "system",
] }
tauri = { version = "2.5.0", features = [] }
tauri-plugin-shell = "2"
walkdir = "2.5.0"
zip = { version = "2.4.2", default-features = false, features = ["deflate"] }
lazy_static = "1.5.0"
