name: "build on macos-tauri"

on:
  workflow_dispatch:
    inputs:
      branch:
        description: "指定分支"
        required: true
        default: "tauri"
        type: string

jobs:
  publish-tauri:
    permissions:
      contents: write
    strategy:
      matrix:
        include:
          - platform: "macos-latest" # for Arm based macs (M1 and above).
            args: "--target aarch64-apple-darwin"
          - platform: "macos-13" # for Intel based macs.
            args: "--target x86_64-apple-darwin"
          # - platform: "ubuntu-22.04" # for Tauri v1 you could replace this with ubuntu-20.04.
          #   args: ""
          - platform: "windows-latest"
            args: ""

    runs-on: ${{ matrix.platform }}
    env:
      APPLE_ID: ${{ secrets.APPLE_ID }}
      APPLE_ID_PASSWORD: ${{ secrets.APPLE_ID_PASSWORD }}
    steps:
      - name: Import Apple Developer Certificate
        if: matrix.platform != 'windows-latest'
        env:
          APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
        run: |
          set -x
          echo $APPLE_CERTIFICATE | base64 --decode > certificate.p12
          security create-keychain -p "$KEYCHAIN_PASSWORD" build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p "$KEYCHAIN_PASSWORD" build.keychain
          security import certificate.p12 -k build.keychain -P "$APPLE_CERTIFICATE_PASSWORD" -T /usr/bin/codesign
          security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k "$KEYCHAIN_PASSWORD" build.keychain
          security find-identity -v -p codesigning build.keychain
          set +x

      - name: Verify Certificate
        if: matrix.platform != 'windows-latest'
        run: |
          CERT_INFO=$(security find-identity -v -p codesigning build.keychain | grep "Developer ID Application")
          CERT_ID=$(echo "$CERT_INFO" | awk -F'"' '{print $2}')
          echo "CERT_ID=$CERT_ID" >> $GITHUB_ENV
          TEAM_ID=$(echo "$CERT_ID" | awk -F '[()]' '{print $2}')
          echo "TEAM_ID=$TEAM_ID" >> $GITHUB_ENV
          echo "Certificate imported."

      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}
          fetch-depth: 0 # 可选：获取完整历史记录
          # submodules: false
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Git to use token only for specific submodule repo
        run: |
          git config --global url."https://${{ secrets.TAURI_APPS_SUBMODULE_TOKEN }}@github.com/AtomInnoLab/tauri-apps.git".insteadOf "https://github.com/AtomInnoLab/tauri-apps.git"

      - name: Initialize and update submodules recursively
        run: git submodule update --init

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: install Rust stable
        uses: dtolnay/rust-toolchain@stable
        # with:
        #   targets: ${{ matrix.platform == 'macos-latest' && 'aarch64-apple-darwin,x86_64-apple-darwin' || '' }}

      - name: install dependencies (ubuntu only)
        if: matrix.platform == 'ubuntu-22.04' # This must match the platform value defined above.
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.0-dev libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf

      - name: install frontend dependencies
        run: npm install # change this to npm, pnpm or bun depending on which one you use.

      - name: download extrnal dep
        if: matrix.platform != 'windows-latest'
        env:
          GITHUB_ACCESS_TOKEN: ${{ secrets.WSI_ACCESS_TOKEN }}
          CERT_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          P12_BASE64: ${{ secrets.APPLE_CERTIFICATE }}
          TEAM_ID: ${{ env.TEAM_ID }}
        run: |
          set + x
          ./scripts/download-dep.sh

      - name: download extrnal dep
        if: matrix.platform == 'windows-latest'
        env:
          GITHUB_ACCESS_TOKEN: ${{ secrets.WSI_ACCESS_TOKEN }}
        run: |
          set + x
          ./scripts\download-dep-win.ps1

      - name: Set proxy
        run: |
          rm ~/.gitconfig
          git config --global url."https://${{ secrets.WSI_ACCESS_TOKEN }}@github.com".insteadOf ssh://**************
          git config --global credential.helper store

      - name: Npm fund
        run: npm fund

      - uses: tauri-apps/tauri-action@v0
        env:
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_PASSWORD: ${{ secrets.APPLE_ID_PASSWORD }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          APPLE_SIGNING_IDENTITY: ${{ env.CERT_ID }}
          APPLE_TEAM_ID: ${{ env.TEAM_ID }}
          GITHUB_ACCESS_TOKEN: ${{ secrets.WSI_ACCESS_TOKEN }}
          CARGO_NET_GIT_FETCH_WITH_CLI: true
        with:
          projectPath: ./src-tauri
          tagName: app-v__VERSION__
          releaseName: "App v__VERSION__"
          releaseBody: "See the assets to download this version and install."
          releaseDraft: true
          prerelease: false
          args: ${{ matrix.args }}

      - name: Notify Feishu
        if: always()
        shell: bash
        run: |
          set -x
          unset http_proxy
          unset https_proxy
          cd ${{ github.workspace }}
          BUILD_STATUS="${{ job.status }}"
          # COMMIT_MSG=$(git log -1 --pretty=format:"%s")
          COMMIT_MSG=$(git log -1 --pretty=format:"%s"|awk 'NR==1{for (i=1; i<=NF; i++) printf "%s ", $i; print ""}')
          COMMIT_AUTHOR=$(git log -1 --pretty=format:"%an")
          COMMIT_SHA=$(git log -1 --pretty=format:"%h")

          if [ "$BUILD_STATUS" = "success" ]; then
            STATUS_TEXT=" Success"
          else
            STATUS_TEXT=" Failed"
          fi
          curl -H "Content-Type: application/json" -d "{
            \"msg_type\": \"post\",
            \"content\": {
              \"post\": {
                \"zh_cn\": {
                  \"title\": \"WisFile-Frontend[${{ github.event.inputs.branch }}] - ${STATUS_TEXT}\",
                  \"content\": [
                    [{\"tag\": \"text\",\"text\": \"Branch: ${{ github.event.inputs.branch }}\\n\"}],
                    [{\"tag\": \"text\",\"text\": \"Commit Message: ${COMMIT_MSG}\\n\"}],
                    [{\"tag\": \"text\",\"text\": \"Author: ${COMMIT_AUTHOR}\\n\"}],
                    [{\"tag\": \"text\",\"text\": \"Commit SHA: ${COMMIT_SHA}\\n\"}],
                    [{\"tag\": \"text\",\"text\": \"Build Status: ${STATUS_TEXT}\\n\"}]
                  ]
                }
              }
            }
          }" https://open.feishu.cn/open-apis/bot/v2/hook/27347fca-da5c-4a26-9e8d-afca409f96c7
