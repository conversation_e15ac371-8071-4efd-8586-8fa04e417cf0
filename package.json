{"name": "wisfile", "private": true, "version": "1.2.20", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noUnusedLocals false && vite build", "preview": "vite preview", "tauri": "tauri", "lint:fix": "eslint 'src/**/*.{ts,tsx}' --fix"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@reduxjs/toolkit": "^2.8.2", "@tauri-apps/api": "2.5.0", "@tauri-apps/plugin-dialog": "~2", "@tauri-apps/plugin-fs": "~2", "@tauri-apps/plugin-http": "~2", "@tauri-apps/plugin-log": "~2", "@tauri-apps/plugin-opener": "^2.4.0", "@tauri-apps/plugin-os": "^2.2.1", "@tauri-apps/plugin-shell": "~2", "@tauri-apps/plugin-store": "~2", "@tauri-apps/plugin-tabs": "file:./vendor/tauri-apps/packages/plugin-tabs", "@types/lodash": "^4.17.17", "antd": "^5.25.1", "axios": "^1.9.0", "classnames": "^2.5.1", "framer-motion": "^12.18.1", "i18next": "^25.1.3", "i18next-browser-languagedetector": "^8.1.0", "idb": "^8.0.3", "lodash": "^4.17.21", "react": "^18.3.1", "react-activation": "^0.13.4", "react-dom": "^18.3.1", "react-i18next": "^15.5.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1", "react-transition-group": "^4.4.5", "swiper": "^11.2.10", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.5"}, "devDependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@eslint/js": "^8.0.0", "@svgr/plugin-jsx": "^8.1.0", "@svgr/plugin-svgo": "^8.1.0", "@tauri-apps/api": "2.5.0", "@tauri-apps/cli": "^2.5.0", "@types/node": "^22.15.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/react-transition-group": "^4.4.12", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react-hooks": "^4.0.0", "eslint-plugin-react-refresh": "^0.4.0", "globals": "^15.15.0", "postcss": "^8", "prettier": "^2.0.0", "sass": "^1.89.2", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^7.0.0", "vite": "^6.3.5", "vite-plugin-svgr": "^4.2.0"}}