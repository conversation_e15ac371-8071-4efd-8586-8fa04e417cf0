# Tauri 插件开发示例

本项目是 Tauri 前端插件库，包括项目示例，提供了完整的插件开发流程和最佳实践。

## 项目结构

```
.
├── example             # 示例应用
├── Makefile            # 项目管理脚本
├── packages            # 插件库
│   ├── plugin-tabs
│   └── plugin-xxx
├── README.md
└── vendor               #submodule
    └── tauri-apps
```
## 代码注释
``` ts
export interface TabsBarProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'translate'> {
  tabs: Tab[];
  activeTabId: string;
  onClose?: () => void;      // 窗口关闭自定义事件
  onTabClick: (id: string) => void;
  onTabClose: (id: string) => void;
  onTabDragEnd: (fromId: string, toId: string) => void;
  onNewTab: () => void;
  className?: string;
  style?: React.CSSProperties;
  showNewTabButton?: boolean;
  newTabButtonText?: string;
  draggable?: boolean;
  enableWindowDrag?: boolean;
  translate?: (key: string) => string; // 多语言函数
}
```

## 环境要求

- Node.js >= 20+ (推荐使用最新的 LTS 版本)

## 安装步骤

1. 克隆项目并初始化 submodule：

```bash
cd [root] # 工程根目录
make submodule-init
make submodule-add
```

2. 添加依赖

```bash
  "dependencies": {
    #...
    "@tauri-apps/plugin-tabs": "file:[root]/vendor/tauri-apps/packages/plugin-tabs",
    #...
  },
```

3. 权限控制
   `src-tauri/capabilities/default.json`
   ``

   ```bash
   "permissions":{
    "core:window:default",
    "core:window:allow-toggle-maximize",
    "core:window:allow-internal-toggle-maximize",
    "core:window:allow-minimize",
    "core:window:allow-close",
    "core:window:allow-hide",
    "core:window:allow-show",
    "core:window:allow-maximize",
    "core:window:allow-unmaximize",
    "core:window:allow-unminimize",
    "core:window:allow-start-dragging",
    "core:window:allow-set-fullscreen",
    "core:window:deny-is-always-on-top",
    "core:window:allow-set-decorations"
   }
   ```

4. 窗口配置

`src-tauri/tauri.conf.json`

```bash
  "app":{
    "windows":[
      {
        "titleBarStyle": "Overlay",
        "hiddenTitle": true,
        "decorations": false
      }
    ]
  }
```

5. 安装依赖

```bash
npm i
```

## 使用指南

在 React 组件中使用 Tabs 插件：

1. 添加 Provider

```ts
const initialTabs = [
  {
    id: "default",
    title: "Default Tab",
    path: "/",
  },
];

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <BrowserRouter>
      <TabsProvider initialTabs={initialTabs}>
        <App />
      </TabsProvider>
    </BrowserRouter>
  </React.StrictMode>
);
```

2. 组件引入

```tsx
import { TabsBar, TabContent, useTabsContext } from "@tauri-apps/plugin-tabs";

function App() {
  const { tabs, activeTabId, setActiveTabId, open, removeTab, reorderTabs } =
    useTabsContext();

  return (
    <TabsBar
      tabs={tabs}
      activeTabId={activeTabId}
      onTabClick={handleTabClick}
      onTabClose={removeTab}
      onTabDragEnd={reorderTabs}
      onNewTab={handleNewTab}
    >
      {renderTabContent}
    </TabsBar>
  );
}
```

## 主要功能

- 标签页管理：添加、删除、切换标签页
- 标签页拖拽排序
- 标签页内容管理

### 注意事项

如果遇到依赖问题，可以尝试：

```bash
make submodule-remove
make submodule-add
```
