#!/usr/bin/env bash

set -euo pipefail

# Detect the operating system
OS="$(uname -s)"
IS_MACOS=false
if [ "$OS" = "Darwin" ]; then
    IS_MACOS=true
fi

GITHUB_ACCESS_TOKEN=$GITHUB_ACCESS_TOKEN
PROJECT_PATH=$(pwd)

if $IS_MACOS; then
    KEY_CHAIN_NAME="wispaper.wasmedge"
    CERT_PASSWORD=$CERT_PASSWORD
    P12_BASE64=$P12_BASE64
    TEAM_ID=$TEAM_ID
fi

_download_wasmedge() {
    rm -rf $1
    mkdir -p $1

    curl -sSf https://raw.githubusercontent.com/StroryBank/WasmEdge/refs/heads/master/utils/install_v2_wiz.sh -o install_wasmedge.sh

    sed -i '' '586,608d' install_wasmedge.sh || true

    chmod +x install_wasmedge.sh
    ./install_wasmedge.sh -p "$wasmedge_path"
    rm install_wasmedge.sh
}

get_wasmedge() {
    local owner="AtomInnoLab"
    local tmp_dir=/tmp/wasmedge
    local filepath="$tmp_dir/llama-api-server.wasm"
    local repo="LlamaEdge"
    local assets_name="llama-api-server.wasm"
    local wasmedge_path="$PROJECT_PATH/src-tauri/resources/wasmedge"
    local llama_path="$wasmedge_path/api"

    rm -rf $PROJECT_PATH/src-tauri/resources/wasmedge

    _download_wasmedge $wasmedge_path
    rm -rf $wasmedge_path/env
    rm -rf $wasmedge_path/include
    rm -rf $wasmedge_path/bin/wasmedgec

    sed -i '' '\/resources\/wasmedge\/env/d' ~/.zshrc || true
    sed -i '' '\/resources\/wasmedge\/env/d' ~/.zprofile || true

    echo "WasmEdge download success"

    rm -rf $tmp_dir
    mkdir -p $tmp_dir

    # get the wisfile-backend release assets id
    local assets_id=$(curl -L \
     -H "Accept: application/vnd.github+json" \
     -H "Authorization: Bearer $GITHUB_ACCESS_TOKEN" \
     -H "X-GitHub-Api-Version: 2022-11-28" \
     https://api.github.com/repos/AtomInnoLab/$repo/releases/latest \
     | jq -r --arg name "$assets_name" '.assets[] | select(.name == $name) | .id')

     echo "select $assets_name, id: $assets_id"

    # download the github release
    download_assets $owner $repo $assets_id $filepath

    mkdir -p $llama_path

    mv $filepath $llama_path

    echo "LlamaApi download success"
    if $IS_MACOS; then
        echo "Start CodeSign"

        # codesign
        # add p12
        echo "add the keychain"
        echo "$P12_BASE64" | base64 --decode > DeveloperID.p12

        # create a new tmp keychains
        echo "create a new tmp keychains"
        if security list-keychains | grep -q "$KEY_CHAIN_NAME"; then
            echo "remove the old tmp keychain"
            security delete-keychain "$KEY_CHAIN_NAME"
        fi

        security create-keychain -p "" $KEY_CHAIN_NAME || true
        security set-keychain-settings $KEY_CHAIN_NAME
        security unlock-keychain -p "" $KEY_CHAIN_NAME
        security import DeveloperID.p12 -k $KEY_CHAIN_NAME -P "$CERT_PASSWORD" -T /usr/bin/codesign
        rm DeveloperID.p12
        security list-keychains -d user -s $KEY_CHAIN_NAME 2>&1 >/dev/null
        security set-key-partition-list -S apple-tool:,apple: -s -k "" $KEY_CHAIN_NAME 2>&1 >/dev/null

        codesign $wasmedge_path

        # remove the keychain
        security delete-keychain $KEY_CHAIN_NAME

        echo "Check CodeSign"
        check_sign $wasmedge_path
    fi

    rm -rf $tmp_dir
}

get_qdrant() {
    local target=$1
    local assets_name="$target.tar.gz"
    local owner="StroryBank"
    local tmp_dir=/tmp/qdrant
    local filepath="$tmp_dir/$assets_name"
    local repo="qdrant"

    rm $PROJECT_PATH/src-tauri/resources/$target || true
    rm -rf $tmp_dir
    mkdir -p $tmp_dir

    # get the wisfile-backend release assets id
    local assets_id=$(curl -L \
     -H "Accept: application/vnd.github+json" \
     -H "Authorization: Bearer $GITHUB_ACCESS_TOKEN" \
     -H "X-GitHub-Api-Version: 2022-11-28" \
     https://api.github.com/repos/$owner/$repo/releases/latest \
     | jq -r --arg name "$assets_name" '.assets[] | select(.name == $name) | .id')

    echo "select $assets_name, id: $assets_id"

    # download the github release
    download_assets $owner $repo $assets_id $filepath

    tar zxf $filepath -C $tmp_dir
    mv $tmp_dir/qdrant $PROJECT_PATH/src-tauri/resources/$target

    rm -rf $tmp_dir
}

get_llamacpp() {
    local target=$1
    local pattern=$2
    local assets_name="$target.zip"
    local owner="ggml-org"
    local tmp_dir=/tmp/llamacpp
    local filepath="$tmp_dir/$assets_name"
    local repo="llama.cpp"
    local resource_dir=$PROJECT_PATH/src-tauri/resources/llamacpp


    local lib_dir
    if [[ "$target" == *"llama-bin-macos-arm64"* ]]; then
        lib_dir="$resource_dir/aarch64-apple-darwin"
    elif [[ "$target" == *"llama-bin-macos-x64"* ]]; then
        lib_dir="$resource_dir/x86_64-apple-darwin"
    elif [[ "$target" == *"llama-bin-linux-x64"* ]]; then
        lib_dir="$resource_dir/x86_64-linux"
    else
        echo "not support yet"
        lib_dir="$resource_dir/x86_64-linux"
    fi

    rm -rf $lib_dir || true
    mkdir -p $resource_dir || true
    mkdir -p $lib_dir || true

    rm -rf $tmp_dir
    mkdir -p $tmp_dir

    # get the wisfile-backend release assets id
    local assets_id=$(curl -L \
     -H "Accept: application/vnd.github+json" \
     -H "Authorization: Bearer $GITHUB_ACCESS_TOKEN" \
     -H "X-GitHub-Api-Version: 2022-11-28" \
     https://api.github.com/repos/$owner/$repo/releases/latest \
     | jq -r --arg regex "$pattern" '.assets[] | select(.name | test($regex)) | .id')

    echo "select $assets_name, id: $assets_id"

    # download the github release
    download_assets $owner $repo $assets_id $filepath

    unzip $filepath -d $tmp_dir

    if $IS_MACOS; then
        if [[ "$target" == *"llama-bin-macos-arm64"* ]]; then
            install_name_tool -add_rpath "@executable_path/../Resources/libs/aarch64-apple-darwin" $tmp_dir/build/bin/llama-server
            install_name_tool -add_rpath "@executable_path/libs/aarch64-apple-darwin" $tmp_dir/build/bin/llama-server
            install_name_tool -add_rpath "@executable_path/../libs/aarch64-apple-darwin" $tmp_dir/build/bin/llama-server
        else
            install_name_tool -add_rpath "@executable_path/../Resources/libs/x86_64-apple-darwin" $tmp_dir/build/bin/llama-server
            install_name_tool -add_rpath "@executable_path/libs/x86_64-apple-darwin" $tmp_dir/build/bin/llama-server
            install_name_tool -add_rpath "@executable_path/../libs/x86_64-apple-darwin" $tmp_dir/build/bin/llama-server
        fi
        echo "Start CodeSign"

        # codesign
        # add p12
        echo "add the keychain"
        echo "$P12_BASE64" | base64 --decode > DeveloperID.p12

        # create a new tmp keychains
        echo "create a new tmp keychains"
        if security list-keychains | grep -q "$KEY_CHAIN_NAME"; then
            echo "remove the old tmp keychain"
            security delete-keychain "$KEY_CHAIN_NAME"
        fi

        security create-keychain -p "" $KEY_CHAIN_NAME
        security set-keychain-settings $KEY_CHAIN_NAME
        security unlock-keychain -p "" $KEY_CHAIN_NAME
        security import DeveloperID.p12 -k $KEY_CHAIN_NAME -P "$CERT_PASSWORD" -T /usr/bin/codesign
        rm DeveloperID.p12
        security list-keychains -d user -s $KEY_CHAIN_NAME 2>&1 >/dev/null
        security set-key-partition-list -S apple-tool:,apple: -s -k "" $KEY_CHAIN_NAME 2>&1 >/dev/null

        codesign $tmp_dir/build/bin

        # remove the keychain
        security delete-keychain $KEY_CHAIN_NAME
    fi

    if [[ "$target" == *"llama-bin-macos-arm64"* ]]; then
        mv $tmp_dir/build/bin/llama-server $resource_dir/../llama-server-aarch64-apple-darwin
        mv $tmp_dir/build/bin/*.dylib $lib_dir
    elif [[ "$target" == *"llama-bin-macos-x64"* ]]; then
        mv $tmp_dir/build/bin/llama-server $resource_dir/../llama-server-x86_64-apple-darwin
        mv $tmp_dir/build/bin/*.dylib $lib_dir
    elif [[ "$target" == *"llama-bin-linux-x64"* ]]; then
        mv $tmp_dir/build/bin/llama-server $resource_dir/../llama-server-x86_64-unknown-linux-gnu
        mv $tmp_dir/build/bin/*.so $lib_dir
    else
        echo "not support yet"
    fi


    rm -rf $tmp_dir
}

# signcode for param 1
codesign() {
    find $1 -type f \( \
        -name "*.dylib" -o \
        -name "*.so" -o \
        -name "*.node" -o \
        -name "*.wasm" -o \
        -perm +111 -exec test -x {} \; \
    \) -print0 | xargs -0 -I {} codesign --deep --force --options runtime --identifier ai.wisfile --sign "$TEAM_ID" "{}"
}

# check code sign for param 1
check_sign() {
    find $1 -type f \( \
        -name "*.dylib" -o \
        -name "*.so" -o \
        -name "*.node" -o \
        -name "*.wasm" -o \
        -perm +111 -exec test -x {} \; \
    \) -exec sh -c '
        echo "Checking signature for: $1"
        codesign -vvv "$1" 2>&1 || echo " ❌ No valid signature: $1"
    ' sh {} \;
}

# download the assets
download_assets() {
    curl -L \
      -H "Authorization: Bearer $GITHUB_ACCESS_TOKEN" \
      -H "X-GitHub-Api-Version: 2022-11-28" \
      -H 'Accept: application/octet-stream' \
      "https://api.github.com/repos/$1/$2/releases/assets/$3" \
      -o $4
}

# get_wasmedge
# get_qdrant qdrant-aarch64-apple-darwin
# get_qdrant qdrant-x86_64-apple-darwin
if $IS_MACOS; then
    get_llamacpp llama-bin-macos-arm64 'llama-.*-bin-macos-arm64'
    get_llamacpp llama-bin-macos-x64 'llama-.*-bin-macos-x64'
else
    get_llamacpp llama-bin-linux-x64 'llama-.*-bin-ubuntu-vulkan-x64'
fi
