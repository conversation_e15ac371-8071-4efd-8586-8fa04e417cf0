#!/usr/bin/env powershell

# 环境变量配置（需提前设置）
$env:GITHUB_ACCESS_TOKEN = $env:GITHUB_ACCESS_TOKEN
$PROJECT_PATH = Get-Location | Select-Object -ExpandProperty Path

# 下载GitHub Release资产
function Download-Assets {
    param (
        [string]$owner,
        [string]$repo,
        [int]$asset_id,
        [string]$output_path
    )
    $url = "https://api.github.com/repos/$owner/$repo/releases/assets/$asset_id"
    $headers = @{
        "Accept" = "application/octet-stream"
    }
    Invoke-WebRequest -Uri $url -Headers $headers -OutFile $output_path
}

# 获取Llama.cpp
function Get-LlamaCpp {
    $target = $args[0]
    $pattern = $args[1]
    $tmp_dir = "$env:temp/llamacpp"
    $resource_dir = "$PROJECT_PATH\src-tauri\resources"
    $lib_dir = "$resource_dir\llamacpp"

    # 清理旧文件
    Remove-Item -Path $tmp_dir, $lib_dir -Recurse -Force -ErrorAction SilentlyContinue
    New-Item -Path $lib_dir -ItemType Directory -Force | Out-Null
    New-Item -Path $tmp_dir -ItemType Directory -Force | Out-Null

    # 获取最新Release资产ID
    $response = Invoke-RestMethod -Uri "https://api.github.com/repos/ggml-org/llama.cpp/releases/latest"
    $asset_id = ($response.assets | Where-Object { $_.name -match $pattern }).id

    # 下载并解压
    $zip_path = "$tmp_dir\$target.zip"
    Download-Assets "ggml-org" "llama.cpp" $asset_id $zip_path
    Expand-Archive -Path $zip_path -DestinationPath $tmp_dir -Force

    # 移动文件到目标位置
    $lib_dir = "$lib_dir\x86-win"

    New-Item -Path $lib_dir -ItemType Directory -Force | Out-Null
    Move-Item -Path "$tmp_dir\*.dll" -Destination $lib_dir -Force
    Move-Item -Path "$tmp_dir\llama-server.exe" -Destination "$resource_dir\llama-server-x86_64-pc-windows-msvc.exe" -Force

    Write-Host "Llama.cpp 部署完成: $lib_dir"
}

# 主执行流程
try {
    # 仅保留Llama.cpp下载逻辑
    if ([System.Environment]::OSVersion.Platform -match "Win") {
        Get-LlamaCpp "llama-bin-win-cpu-x64" 'llama-.*-bin-win-cpu-x64'
    }
    else {
        # 非Windows系统处理（可选）
        Write-Host "非Windows系统需手动适配"
    }
}
catch {
    Write-Host "错误: $_" -ForegroundColor Red
    exit 1
}
finally {
    Remove-Item -Path "$env:temp" -Recurse -Force -ErrorAction SilentlyContinue
}