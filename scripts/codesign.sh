#!/bin/bash
<PERSON><PERSON><PERSON>_CHAIN_NAME="build.keychain"

add_keychain() {
    # add p12
    echo "add the keychain"
    echo "$P12_BASE64" | base64 --decode > DeveloperID.p12

    # create a new tmp keychains
    if security list-keychains | grep -q "$KEY_CHAIN_NAME"; then
        echo "remove the old tmp keychain"
        security delete-keychain "$KEY_CHAIN_NAME"
    fi

    security create-keychain -p "" $KEY_CHAIN_NAME
    security unlock-keychain -p "" $KEY_CHAIN_NAME
    security import DeveloperID.p12 -k $KEY_CHAIN_NAME -P "$CERT_PASSWORD" -T /usr/bin/codesign
    security list-keychains -d user -s $KEY_CHAIN_NAME
    rm DeveloperID.p12
}

include_notarization_env() {
   export APPLE_ID="<EMAIL>"
   export APPLE_PASSWORD="zdwm-jspx-vtkd-lqws"
   export APPLE_TEAM_ID="WQXWZGR6QW"
}

add_keychain
include_notarization_env
