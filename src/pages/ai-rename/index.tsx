import { Checkbox, Select } from 'antd';
import { useTranslation } from 'react-i18next';
import UploadFile from '@/components/FileUpload';
import { useEffect, useMemo, useState, useCallback } from 'react';
import type { FileItem } from '@/types/file';
import { getShortFileName } from '@/utils/string';
import { open as openDialog } from '@tauri-apps/plugin-dialog';
import { TauriStore } from '@/utils/store';
import { uiEvent, unwatch, watch } from '@/api/api';
import { error, info } from '@tauri-apps/plugin-log';
import { useTabsContext } from '@tauri-apps/plugin-tabs';
import { useTasksStore } from '@/store/tasksStore';
import { useSearchParams } from 'react-router-dom';
import { CUSTOM_FIELD_V2, SELECT_FIELD_V2 } from '@/const/localStorage';
import { motion, AnimatePresence } from 'framer-motion';
import classNames from 'classnames';
import TruncatedText from '@/components/TruncatedText';
import { defaultFields } from '@/utils/fileTemplate';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import { dirActions } from '@/store/slices/dir';

export default function AIRename() {
  const [searchParams] = useSearchParams();
  const { t } = useTranslation();
  const [files, setFiles] = useState<FileItem[]>([]);
  const [classifyFilesSameTime, setClassifyFilesSameTime] = useState(false);
  const { open } = useTabsContext();
  const { setTaskState } = useTasksStore();
  const nopCache = searchParams.get('nopCache') === 'true';
  const [field, setField] = useState<string | null>();
  const [hasMounted, setHasMounted] = useState(false);

  const dir = useSelector((state: RootState) => state.dir);
  const { embeddingData, directoryPath } = dir;
  const dispatchRTK = useDispatch();

  // 开始重命名的函数
  const handleStart = useCallback(async () => {
    if (files.length === 0 || (classifyFilesSameTime && !directoryPath)) return;

    if (embeddingData.status != 'success' && classifyFilesSameTime) {
      dispatchRTK(dirActions.toggleEmbedding(true));
      return;
    }

    const tabKey = `process_${Date.now()}`;
    const fieldsStr = localStorage.getItem(SELECT_FIELD_V2);
    const fields = JSON.parse(fieldsStr || '[]');

    setTaskState(tabKey, {
      currentTaskMode: 'rename',
      files,
      classifyFilesSameTime,
      directoryPath,
      formatList:
        fields?.map((item: any) => {
          return { ...item, zhCn: true };
        }) || [],
      pendingFiles: [],
      applied: false,
      editingRowPath: '',
    });
    open({
      id: tabKey,
      title: getShortFileName(files[0]?.name || ''),
      path: `/tasks/${tabKey}?tabKey=${tabKey}`,
    });
  }, [
    files,
    classifyFilesSameTime,
    directoryPath,
    embeddingData.status,
    dispatchRTK,
    setTaskState,
    open,
  ]);

  // 将函数暴露到全局，供 Embedding 组件调用
  useEffect(() => {
    (window as any).handleRenameStart = handleStart;
    return () => {
      delete (window as any).handleRenameStart;
    };
  }, [handleStart]);

  useEffect(() => {
    if (field) {
      localStorage.setItem(SELECT_FIELD_V2, field);
    }
  }, [field]);

  // 初始化数据
  useEffect(() => {
    initData();
  }, []);

  // Set hasMounted to true after component has mounted
  useEffect(() => {
    setHasMounted(true);
  }, []);

  // 初始化数据
  useEffect(() => {
    info(`nopCache: ${nopCache}`);
    if (nopCache) {
      setFiles([]);
      setClassifyFilesSameTime(false);
    }
  }, [nopCache]);

  const currentFields = useMemo(() => {
    const cf = localStorage.getItem(CUSTOM_FIELD_V2);
    if (cf) {
      try {
        const parsedFormat = JSON.parse(cf);
        return [...defaultFields, ...parsedFormat];
      } catch (error) {
        return [...defaultFields];
      }
    }
    return [...defaultFields];
  }, []);

  useEffect(() => {
    const storedField = localStorage.getItem(SELECT_FIELD_V2);
    if (storedField) {
      // 判断 storedField 是否 在currentFields 中存在
      const parsedField = JSON.parse(storedField);
      const isFieldValid = currentFields.some(item => {
        return JSON.stringify(item) === JSON.stringify(parsedField);
      });
      if (!isFieldValid) {
        // 如果不存在，则使用默认的第一个字段
        setField(JSON.stringify(defaultFields[0]));
        return;
      }

      setField(storedField);
    } else {
      // 默认选中第一个
      setField(JSON.stringify(defaultFields[0]));
    }
  }, [currentFields]);

  const initData = async () => {
    const store = await TauriStore.create();
    // 获取目录
    const directoryPath = await store.get('directoryPath');
    if (directoryPath) {
      dispatchRTK(dirActions.updateDirectoryPath(directoryPath));
    }
  };

  const onSelectFolder = async () => {
    const selected = await openDialog({
      multiple: false,
      directory: true,
    });
    if (selected) {
      dispatchRTK(dirActions.updateDirectoryPath(selected));

      try {
        await unwatch([selected]);
      } catch (err) {
        error(`[unwatch Error] ${err} ${selected}`);
      }
      try {
        await watch([selected]);
      } catch (err) {
        error(`[watch Error] ${err} ${selected}`);
      }
    }
  };

  return (
    <>
      <div className="relative bg-white shadow-sm px-20 pb-6 rounded-xl w-full min-h-full">
        {files.length === 0 && (
          <AnimatePresence mode="wait">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="flex justify-center pt-[40px] pb-[36px] w-full font-bold text-[20px]"
            >
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2, delay: 0.1 }}
              >
                {t('tidyYourFilesWithAI')}
              </motion.span>
            </motion.div>
          </AnimatePresence>
        )}
        <UploadFile
          onFileSelected={files => {
            setFiles(files.map(file => ({ ...file, rStatus: 'Waiting', cStatus: 'Waiting' })));
          }}
          files={files}
        />

        <div className="flex items-center mt-[40px]">
          <div className="min-w-[200px] font-bold text-[#333] text-[14px]">
            {t('chooseNamingFormat')}
          </div>
          <div className="min-w-40"></div>
          <Select
            className="flex-1 overflow-hidden"
            value={field}
            onChange={value => {
              const item = currentFields.find(item => {
                return JSON.stringify(item) === value;
              });
              if (item) {
                uiEvent('Name', {
                  NameFormat: item.map((field: any) => field.id).join(''),
                });
              }
              setField(value);
            }}
            options={currentFields.map(item => {
              return {
                label: item.map((field: any) => t(field.key)).join(' - '),
                value: JSON.stringify(item),
              };
            })}
            optionRender={oriOption => {
              return (
                <TruncatedText title={String(oriOption.label ?? '')} className="w-full">
                  {String(oriOption.label ?? '')}
                </TruncatedText>
              );
            }}
            labelRender={props => {
              return (
                <TruncatedText title={String(props.label ?? '')} className="w-full">
                  {String(props.label ?? '')}
                </TruncatedText>
              );
            }}
          ></Select>
        </div>

        <AnimatePresence>
          {classifyFilesSameTime && (
            <motion.div
              initial={hasMounted ? { opacity: 0, height: 0, scale: 0.95 } : false}
              animate={{ opacity: 1, height: 'auto', scale: 1 }}
              exit={{ opacity: 0, height: 0, scale: 0.95 }}
              transition={{
                height: { duration: 0.3, ease: 'easeOut' },
                opacity: { duration: 0.3, ease: [0.4, 0, 0.2, 1] },
                scale: { duration: 0.3, ease: 'easeOut' },
              }}
              className="overflow-hidden"
            >
              <div className="flex items-center mt-[40px]">
                <div className="min-w-[200px] font-bold text-[#333] text-[14px]">
                  {t('chooseTargetDirectory')}
                </div>
                <div className="w-40"></div>
                <div className="flex flex-1 items-center border border-[#EBEBEB] rounded-[8px] h-[40px] text-[#333] text-[12px] overflow-hidden min-w-0">
                  <TruncatedText className="px-[12px] min-w-0" title={directoryPath}>
                    {directoryPath}
                  </TruncatedText>
                  <div className="bg-[#EBEBEB] w-[1px] h-[80%] flex-shrink-0"></div>
                  <div
                    className="px-[16px] text-[#333] cursor-pointer flex-shrink-0 whitespace-nowrap"
                    onClick={onSelectFolder}
                  >
                    {t('selectFile')}
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <Checkbox
          checked={classifyFilesSameTime}
          onChange={e => {
            uiEvent('Click', e.target.checked ? { Folder: 'yes' } : { Folder: 'cancel' });
            setClassifyFilesSameTime(e.target.checked);
          }}
          className="mt-[40px]"
        >
          <span className="text-[#333] text-[12px]"> {t('classifyFilesSameTime')}</span>
        </Checkbox>

        <motion.div
          data-testid="start-button"
          onClick={handleStart}
          className={classNames('flex items-center rounded-[8px] px-12 h-[38px] w-fit mx-auto', {
            'cursor-pointer bg-[#FFDF75] text-[#333]': !(
              files.length === 0 ||
              (classifyFilesSameTime && !directoryPath)
            ),
            'cursor-not-allowed bg-[#FFEFB7] text-[#999]':
              files.length === 0 || (classifyFilesSameTime && !directoryPath),
          })}
          initial={false}
          animate={{
            marginTop: classifyFilesSameTime ? '44px' : '80px',
          }}
          transition={{
            duration: 0.3,
            marginTop: {
              duration: hasMounted ? 0.3 : 0,
            },
          }}
        >
          {t('start')}
        </motion.div>
      </div>
    </>
  );
}
