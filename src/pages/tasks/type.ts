export type RenameClassify =
  | { event: 'rename'; data: { src: string; target: string; newSrc?: string } }
  | { event: 'renameError'; data: { src: string; errMsg: string; newSrc?: string } }
  | { event: 'classify'; data: { src: string; target: string; newSrc?: string } }
  | { event: 'classifyProcess'; data: { src: string; total: number } }
  | { event: 'classifyError'; data: { src: string; errMsg: string; newSrc?: string } };
