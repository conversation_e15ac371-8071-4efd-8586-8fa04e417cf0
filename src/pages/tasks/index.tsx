import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { useEffect, useMemo } from 'react';
import type { FileItem } from '@/types/file';
import { Table, Modal, notification } from 'antd';
import { moveFile, renameAndClassify, renameFile, uiEvent } from '@/api/api';
import { Channel } from '@tauri-apps/api/core';
import { error, info } from '@tauri-apps/plugin-log';
import { useTabsContext } from '@tauri-apps/plugin-tabs';
import { useTasksStore } from '@/store/tasksStore';
import classNames from 'classnames';
import { columns } from './var';
import { useMinHeight } from '@/hooks/useMinHeight';

type RenameClassify =
  | { event: 'rename'; data: { src: string; target: string; newSrc?: string } }
  | { event: 'renameError'; data: { src: string; errMsg: string; newSrc?: string } }
  | { event: 'classify'; data: { src: string; target: string; newSrc?: string } }
  | { event: 'classifyError'; data: { src: string; errMsg: string; newSrc?: string } }
  | { event: 'classifyProcess'; data: { src: string; progress: string } };

export default function Tasks() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();

  const { activeTabId, removeTab, open } = useTabsContext();
  const [api, contextHolder] = notification.useNotification();
  const maxTaskCount = 5; // 最大任务数
  const tabKey = searchParams.get('tabKey') || '';
  const { getTaskState, setTaskState, updateFile, updatePendingFile, addPendingFiles } =
    useTasksStore();

  const {
    files,
    pendingFiles,
    currentTaskMode,
    renameFilesSameTime,
    classifyFilesSameTime,
    directoryPath,
    formatList,
    applied,
  } = getTaskState(tabKey);
  const { ref: divRef, isVisible } = useMinHeight(1.5);

  useEffect(() => {
    info(`[Tasks] pendingFiles 变动: ${JSON.stringify(pendingFiles)}`);
  }, [pendingFiles]);

  useEffect(() => {
    info(`[Tasks] Files 变动: ${JSON.stringify(files)}`);
    console.log(`[Tasks] Files 变动: ${JSON.stringify(files)}`);
  }, [files]);

  // 初始化任务
  useEffect(() => {
    if (files && files.length > 0) {
      const unfinishedFiles = files.filter(f => {
        return !(
          f.cStatus.toLowerCase().includes('Finished'.toLowerCase()) ||
          f.cStatus.toLowerCase().includes('Failed'.toLowerCase()) ||
          f.rStatus.toLowerCase().includes('Finished'.toLowerCase()) ||
          f.rStatus.toLowerCase().includes('Failed'.toLowerCase())
        );
      });
      if (unfinishedFiles.length === 0) {
        return;
      }
      startWork(unfinishedFiles);
    }
  }, []);

  const startWork = async (fileList: FileItem[]) => {
    // 开始处理任务
    setTaskState(tabKey, {
      applied: true,
    });

    async function batchProcessFiles(
      tabKey: string,
      files: FileItem[],
      batchSize: number,
      mode: string
    ) {
      for (let i = 0; i < files.length; i += batchSize) {
        const batch = files.slice(i, i + batchSize);

        // 批量更新状态为 Processing
        batch.forEach(file => {
          updateFile(tabKey, file.path, {
            cStatus: 'Processing',
            rStatus: 'Processing',
            progress: 0,
          });
        });

        try {
          await processBatch(tabKey, batch, mode);
        } catch (err) {
          error(`处理批次失败: ${JSON.stringify(err)}`);
          batch.forEach(file => {
            updateFile(tabKey, file.path, {
              cStatus: 'Failed',
              rStatus: 'Failed',
              msg: err instanceof Error ? err.message : 'error',
            });
          });
        }
      }
    }
    await batchProcessFiles(tabKey, fileList, maxTaskCount, currentTaskMode);
  };

  useEffect(() => {
    const allApplied = files.find(file => {
      if (currentTaskMode === 'rename' && classifyFilesSameTime) {
        return (
          (file.rStatus !== 'ApplyFinished' && file.rStatus !== 'Failed') ||
          (file.cStatus !== 'ApplyFinished' && file.cStatus !== 'Failed')
        );
      }

      if (currentTaskMode === 'classify' && renameFilesSameTime) {
        return (
          (file.cStatus !== 'ApplyFinished' && file.cStatus !== 'Failed') ||
          (file.rStatus !== 'ApplyFinished' && file.rStatus !== 'Failed')
        );
      }

      if (currentTaskMode === 'rename') {
        return file.rStatus !== 'ApplyFinished' && file.rStatus !== 'Failed';
      }
      if (currentTaskMode === 'classify') {
        return file.cStatus !== 'ApplyFinished' && file.cStatus !== 'Failed';
      }
    });

    setTaskState(tabKey, {
      applied: allApplied ? false : true,
    });
  }, [files, setTaskState, tabKey]);

  const newColumns = useMemo(() => {
    return columns.map(col => {
      return {
        ...col,
        title: t(col.title),
      };
    });
  }, [t]);

  // 处理单个批次
  async function processBatch(tabKey: string, batch: FileItem[], mode: string) {
    return new Promise(resolve => {
      const onEvent = new Channel<RenameClassify>();
      let completedCount = 0;
      onEvent.onmessage = message => {
        dealMessage(tabKey, message);
        completedCount++;
        if (completedCount === batch.length) {
          resolve(true);
        }
      };

      let targetPath = null;
      let renameTags = null;

      if (mode === 'rename') {
        renameTags = formatList;
        if (classifyFilesSameTime) {
          targetPath = directoryPath;
        }
      } else if (mode === 'classify') {
        targetPath = directoryPath;
        if (renameFilesSameTime) {
          renameTags = formatList;
        }
      }

      const onlyClassify = mode === 'classify' && !renameFilesSameTime;

      renameAndClassify(
        {
          source: batch.map(file => file.path),
          target: targetPath,
          onlyClassify,
          renameTags: renameTags || [],
        },
        onEvent
      );
    });
  }

  const dealMessage = async (tabKey: string, message: RenameClassify) => {
    const path = message.data.src;

    info(`[处理消息] 事件: ${message.event}, 路径: ${path}, 数据: ${JSON.stringify(message.data)}`);

    // 准备文件更新数据
    let fileUpdate: Partial<FileItem> = {
      progress: 100,
    };

    switch (message.event) {
      case 'rename':
        fileUpdate = {
          ...fileUpdate,
          rStatus: 'Finished',
          renaming: message.data.target,
        };
        break;

      case 'renameError':
        fileUpdate = {
          ...fileUpdate,
          rStatus: 'Failed',
          msg: message.data.errMsg,
        };
        break;

      case 'classify':
        fileUpdate = {
          ...fileUpdate,
          cStatus: 'Finished',
          classifying: message.data.target,
        };
        break;

      case 'classifyError':
        fileUpdate = {
          ...fileUpdate,
          cStatus: 'Failed',
          msg: message.data.errMsg,
        };
        break;

      default:
        return;
    }

    info(`[处理消息] 更新文件状态: ${JSON.stringify(fileUpdate)}`);
    // 更新文件状态
    updateFile(tabKey, path, fileUpdate);

    // 处理待处理文件（手动模式）
    if (message.event === 'classify' || message.event === 'rename') {
      const currentState = getTaskState(tabKey);
      const file = currentState.files.find(f => f.path === path);
      info(`[处理消息] 当前文件状态: ${JSON.stringify(file)}`);

      if (!file) return;

      let shouldAddFile = false;

      if (currentTaskMode === 'rename') {
        if (classifyFilesSameTime) {
          shouldAddFile = file.rStatus === 'Finished' && file.cStatus === 'Finished';
        } else {
          shouldAddFile = file.rStatus === 'Finished';
        }
      } else if (currentTaskMode === 'classify') {
        if (renameFilesSameTime) {
          shouldAddFile = file.cStatus === 'Finished' && file.rStatus === 'Finished';
        } else {
          shouldAddFile = file.cStatus === 'Finished';
        }
      }

      if (shouldAddFile) {
        info(`[处理消息] 添加待处理文件: ${JSON.stringify(file)}`);
        addPendingFiles(tabKey, [file]);
      }
    }
  };

  // 手动应用
  const applyFiles = async () => {
    info(`[手动应用] 待处理文件数: ${pendingFiles.length}`);

    if (pendingFiles.length === 0) {
      openNotification(false, t('noFilesToProcess'));
      return;
    }

    let successCount = 0;
    let failedCount = 0;
    const errorMessages: string[] = [];

    for (const pendingFile of pendingFiles) {
      const theFile = files.find(f => f.path === pendingFile.path);
      info(`[处理文件] 正在处理文件tabKey: ${tabKey}`);
      info(`[处理文件] 正在处理文件files: ${JSON.stringify(files)}`);
      info(`[处理文件] 正在处理文件pendingFile: ${JSON.stringify(pendingFile)}`);
      info(`[处理文件] 正在处理文件theFile: ${JSON.stringify(theFile)}`);
      info(`[处理文件] ${pendingFile.path} 状态: ${theFile?.rStatus}, ${theFile?.cStatus}`);

      if (!theFile) {
        continue; // 如果文件不存在，跳过处理
      }

      if (currentTaskMode === 'rename' && classifyFilesSameTime) {
        // 两个同时判断
        if (theFile?.renaming && theFile?.classifying) {
          const source = pendingFile.path;
          const targetRename = theFile.renaming;
          const targetClassify = theFile.classifying;
          if (
            theFile.rStatus === 'ApplyFinished' &&
            source.includes(targetRename) &&
            theFile.cStatus === 'ApplyFinished' &&
            source.includes(targetClassify)
          ) {
            info(`[跳过] 文件 ${pendingFile.path} 重命名和分类目标与源相同，跳过处理`);
            continue;
          }
        }
      } else if (currentTaskMode === 'rename') {
        if (theFile?.renaming) {
          const source = pendingFile.path;
          const target = theFile.renaming;
          if (theFile.rStatus === 'ApplyFinished' && source.includes(target)) {
            info(`[跳过] 文件 ${pendingFile.path} 重命名目标与源相同，跳过处理`);
            continue;
          }
        }
      }
      if (currentTaskMode === 'classify' && renameFilesSameTime) {
        if (theFile?.renaming && theFile?.classifying) {
          const source = pendingFile.path;
          const targetRename = theFile.renaming;
          const targetClassify = theFile.classifying;
          if (
            theFile.rStatus === 'ApplyFinished' &&
            source.includes(targetRename) &&
            theFile.cStatus === 'ApplyFinished' &&
            source.includes(targetClassify)
          ) {
            info(`[跳过] 文件 ${pendingFile.path} 重命名和分类目标与源相同，跳过处理`);
            continue;
          }
        }
      } else if (currentTaskMode === 'classify') {
        if (theFile?.classifying) {
          const source = pendingFile.path;
          const target = theFile.classifying;
          if (theFile.cStatus === 'ApplyFinished' && source.includes(target)) {
            info(`[跳过] 文件 ${pendingFile.path} 分类目标与源相同，跳过处理`);
            continue;
          }
        }
      }

      try {
        // 1. 标记为 Processing
        updateFile(tabKey, pendingFile.path, {
          rStatus: 'Processing',
          cStatus: 'Processing',
          progress: 0,
        });

        let newPath = pendingFile.path;
        const operationLog = [];
        const updatePayload: Partial<FileItem> = {};

        // 2. 处理重命名
        if (pendingFile.renaming) {
          try {
            const renameRes = await renameFile({
              source: newPath,
              target: pendingFile.renaming || '',
            });

            info(`[重命名结果] ${JSON.stringify(renameRes)}`);

            if (!renameRes.success) {
              throw new Error(`rename failed: ${renameRes.msg}`);
            }

            newPath = renameRes.msg || newPath;
            operationLog.push(`rename success: ${newPath}`);
            updatePayload.rStatus = 'ApplyFinished';
          } catch (error) {
            console.log(`[处理失败] 000文件 ${pendingFile.path} 处理失败: ${error}`);

            const errorMsg = error instanceof Error ? error.message : String(error);
            errorMessages.push(`file ${pendingFile.path} process failed: ${errorMsg}`);
            info(`[处理失败] 文件 ${pendingFile.path} 处理失败: ${errorMsg}`);
            updatePayload.rStatus = 'Failed';
            updatePayload.msg = errorMsg;
            // 跳过后续移动操作
            throw error;
          }
        }

        // 3. 处理移动
        if (pendingFile.classifying) {
          try {
            const moveRes = await moveFile({
              source: newPath,
              target: pendingFile.classifying || '',
            });

            info(`[移动结果] ${JSON.stringify(moveRes)}`);

            if (!moveRes.success) {
              throw new Error(`move failed: ${moveRes.msg}`);
            }

            newPath = moveRes.msg || newPath;
            operationLog.push(`move success: ${newPath}`);
            updatePayload.cStatus = 'ApplyFinished';
          } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            errorMessages.push(`file ${pendingFile.path} process failed: ${errorMsg}`);
            info(`[处理失败] 文件 ${pendingFile.path} 处理失败: ${errorMsg}`);
            updatePayload.cStatus = 'Failed';
            updatePayload.msg = (updatePayload.msg ? `${updatePayload.msg}; ` : '') + errorMsg;
            throw error;
          }
        }

        info(`[处理完成] 文件 ${pendingFile.path} 处理完成，新路径: ${newPath}`);

        // 4. 统一更新最终状态
        updateFile(tabKey, pendingFile.path, {
          ...updatePayload,
          path: newPath,
          progress: 100,
          msg: operationLog.join('; '),
        });

        updatePendingFile(tabKey, pendingFile.path, {
          ...pendingFile,
          ...updatePayload,
          path: newPath,
          progress: 100,
          msg: operationLog.join('; '),
        });

        successCount++;
      } catch (error) {
        // 捕获重命名或移动中的异常
        const errorMsg = error instanceof Error ? error.message : String(error);
        info(`[处理失败] 文件 ${pendingFile.path} 处理失败: ${errorMsg}`);
        info(`[处理失败] 错误详情: ${JSON.stringify(error)}`);
        info(`[处理失败] pendingFile: ${JSON.stringify(pendingFile)}`);

        // 更新失败状态
        const failedUpdate = files.find(f => f.path === pendingFile.path);
        updateFile(tabKey, pendingFile.path, {
          ...failedUpdate,
          msg: errorMsg,
        });

        updatePendingFile(tabKey, pendingFile.path, {
          ...pendingFile,
          ...failedUpdate,
          msg: errorMsg,
        });

        failedCount++;
        continue; // 跳过当前文件，继续处理下一个
      }
    }

    // 更新任务状态
    setTaskState(tabKey, {
      applied: true,
    });

    // 显示汇总通知
    if (failedCount === 0) {
      openNotification(true, t('applySuccessCount', { count: successCount }));
    } else {
      openNotification(
        false,
        t('applyPartialSuccess', { success: successCount, failed: failedCount }),
        errorMessages.join('\n')
      );
    }
  };

  const openNotification = (pauseOnHover: boolean, message?: string, description?: string) => {
    api.open({
      message: message || '',
      description: description || '',
      pauseOnHover,
    });
  };

  const data = useMemo(() => {
    return (
      files &&
      files.map((f: FileItem) => ({
        key: f.path,
        rStatus: f.rStatus,
        cStatus: f.cStatus,
        file: f.name,
        renaming: f.renaming,
        classifying: f.classifying,
        msg: f.msg,
        path: f.path,
        name: f.name,
        progress: f.progress || 0,
      }))
    );
  }, [files]);

  return (
    <>
      {contextHolder}
      <div className="flex flex-col pr-3 pb-3 h-full">
        <div className="flex flex-col bg-white shadow-md p-6 pb-3 rounded-xl h-full">
          <div className="flex flex-col flex-1 overflow-x-hidden overflow-y-auto">
            <Table
              bordered
              columns={newColumns.filter(item => {
                if (
                  currentTaskMode === 'rename' &&
                  item.dataIndex === 'classify' &&
                  !classifyFilesSameTime
                ) {
                  return false;
                }

                return true;
              })}
              dataSource={data}
              pagination={false}
              rowKey={record => record.path}
              rowClassName={record => {
                if (
                  (record.rStatus === 'Waiting' || record.cStatus === 'Waiting') &&
                  (currentTaskMode === 'rename' || currentTaskMode === 'classify')
                ) {
                  return 'task-row processing-row-waiting';
                }

                return `task-row`;
              }}
            />
            <div
              ref={divRef}
              className={classNames('flex-1 border border-t-0 rounded-b-lg', {
                hidden: !isVisible,
              })}
            />
          </div>
          <div className="flex justify-between items-center mt-4">
            <span className="text-[#999] text-[12px]">
              {data ? data.length : 0} {t('unit')}
            </span>
            <div className="flex items-center gap-x-3">
              <div
                className="flex justify-center items-center border border-[#EBEBEB] rounded-lg w-[128px] h-[38px] text-[#333] cursor-pointer"
                onClick={() => {
                  if (applied) {
                    uiEvent('Click', { Back: 'after_apply' });
                    removeTab(activeTabId);
                    const title = currentTaskMode === 'rename' ? 'renameFiles' : 'autoClassify';
                    open({ id: currentTaskMode, title, path: currentTaskMode });
                  } else {
                    uiEvent('Click', { Back: 'before_apply' });
                    const m = Modal.confirm({
                      width: 460,
                      centered: true,
                      content: (
                        <div>
                          <span className="font-normal text-[#333] text-[14px]">
                            {t('confirmCancel')}
                          </span>
                          <div className="flex justify-center items-center gap-x-2 mt-4">
                            <div
                              className="flex justify-center items-center border border-[#EBEBEB] rounded-lg w-[100px] h-[34px] cursor-pointer"
                              onClick={() => {
                                m.destroy();
                              }}
                            >
                              {t('Cancel')}
                            </div>
                            <div
                              className={classNames(
                                'flex justify-center items-center border rounded-lg w-[100px] h-[34px] cursor-pointer bg-[#FFDF75]  text-[#333] border-[#EBEBEB]'
                              )}
                              onClick={() => {
                                removeTab(activeTabId);
                                const title =
                                  currentTaskMode === 'rename' ? 'renameFiles' : 'autoClassify';
                                open({ id: currentTaskMode, title, path: currentTaskMode });
                                m.destroy();
                              }}
                            >
                              {t('OK')}
                            </div>
                          </div>
                        </div>
                      ),
                      title: null,
                      okCancel: false,
                      icon: null,
                      footer: null,
                    });
                  }
                }}
              >
                {t('back')}
              </div>
              {
                <div
                  className={classNames(
                    'flex items-center justify-center rounded-lg w-[128px] h-[38px]',
                    {
                      'bg-[#FFEFB7] cursor-not-allowed text-[#999]': applied,
                      'bg-[#FFDF75] cursor-pointer text-[#333]': !applied,
                    }
                  )}
                  onClick={() => {
                    !applied && applyFiles();
                    !applied && uiEvent('Click', { Apply: 'all' });
                  }}
                >
                  {t('apply')}
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
