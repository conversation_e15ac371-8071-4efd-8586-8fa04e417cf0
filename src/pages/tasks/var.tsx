import FileName from '@/components/Task/FileName';
import Folder from '@/components/Task/Folder';
import Operation from '@/components/Task/Operation';

export const columns = [
  {
    title: 'fileName',
    dataIndex: 'fileName',
    key: 'fileName',
    ellipsis: true,
    render: (_: any, record: any) => {
      return <FileName record={record} />;
    },
  },
  {
    title: 'classify',
    dataIndex: 'classify',
    key: 'classify',
    width: 150,
    render: (_: any, record: any) => {
      return <Folder record={record} />;
    },
  },
  {
    title: 'operation',
    dataIndex: 'operation',
    key: 'operation',
    width: 175,
    render: (_: any, record: any) => {
      return <Operation record={record} />;
    },
  },
];
