import React, { useCallback, useMemo, useEffect } from 'react';
import { TabsBar, TabContent, useTabsContext } from '@tauri-apps/plugin-tabs';
import { useNavigate, Routes, Route } from 'react-router-dom';

import { useTranslation } from 'react-i18next';
import { getSelectedModel, switchModel } from '@/api/api';
import { info } from '@tauri-apps/plugin-log';
import Layout from '../layout';
import { routerList } from '../router';
export interface CustomTab {
  id: string;
  path?: string;
  content?: React.ReactNode;
}

export default function TheHome() {
  const navigate = useNavigate();

  const { t } = useTranslation();
  const { tabs, activeTabId, setActiveTabId, removeTab, reorderTabs } = useTabsContext();

  // onCreate
  useEffect(() => {
    // 启动模型
    initData();
  }, []);

  const initData = async () => {
    // 获取模型启动参数
    const model = await getSelectedModel();
    if (model) {
      info(`[startLocalModel] ${JSON.stringify(model)}`);
      await switchModel(model);
    } else {
      info(`[startOnlineModel] 默认启动在线模型`);
      await switchModel({
        rename: { type: 'Online' },
        embedding: { type: 'Online' },
      });
    }
  };

  // 标签切换
  const handleTabClick = useCallback(
    (tabId: string) => {
      const tab = tabs.find(t => t.id === tabId) as CustomTab;
      if (tab?.path) {
        navigate(tab.path);
      }
      setActiveTabId(tabId);
    },
    [tabs, navigate, setActiveTabId]
  );

  // 新建标签
  const handleNewTab = useCallback(() => {}, []);

  // 渲染标签内容
  const renderTabContent = useMemo(() => {
    return (
      <Routes>
        {routerList.map(route => {
          return (
            <Route key={route.path} path={route.path} element={<TabContent />}>
              <Route element={<Layout />}>
                <Route index element={route.element} />
              </Route>
            </Route>
          );
        })}
      </Routes>
    );
  }, []);

  return (
    <>
      <TabsBar
        tabs={tabs}
        activeTabId={activeTabId}
        onTabClick={handleTabClick}
        onTabClose={removeTab}
        onTabDragEnd={reorderTabs}
        onNewTab={handleNewTab}
        showNewTabButton={false}
        translate={t}
      >
        {renderTabContent}
      </TabsBar>
    </>
  );
}
