import { LOCAL_LANGUAGE } from '@/const/language';
import { TauriStore } from '@/utils/store';
import { useLayoutEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { languages } from './var';
import UpdateIcon from '@/assets/svg/update.svg?react';
import EmailIcon from '@/assets/svg/email.svg?react';
import { getVersion } from '@tauri-apps/api/app';
import { DOWNLOAD_URL } from '@/utils/constants';
import { Select } from 'antd';
import ModelDownload from '@/components/Settings/ModelDownload';
import FileTemplate from '@/components/Settings/FileTemplate';
import { openUrl } from '@tauri-apps/plugin-opener';
import { uiEvent } from '@/api/api';

export default function Settings() {
  const [version, setVersion] = useState('');
  const { t, i18n } = useTranslation();
  const [language, setLanguage] = useState('en');

  useLayoutEffect(() => {
    // 语言初始化
    TauriStore.create().then(s => {
      s.get(LOCAL_LANGUAGE).then(l => {
        setLanguage(l);
      });
    });

    // 版本配置
    try {
      getVersion().then(v => {
        setVersion(v || '');
      });
    } catch (e) {
      setVersion('');
    }
  }, []);

  return (
    <div className="flex mx-auto mr-3 min-h-full">
      <div className="bg-white shadow-md mb-[16px] p-10 rounded-xl w-full">
        <div className="flex items-center">
          <div className="min-w-40 font-bold text-[#333] text-[14px]">{t('language')}</div>
          <Select
            value={language}
            onChange={value => {
              uiEvent('Click', { Language: value });
              TauriStore.create().then(store => {
                store.set(LOCAL_LANGUAGE, value);
              });
              setLanguage(value);
              i18n.changeLanguage(value);
            }}
            options={languages}
            className="w-48"
          />
        </div>
        <div className="mt-[30px] border-[#EBEBEB] border-b"></div>
        <ModelDownload />
        <div className="mt-[40px] border-[#EBEBEB] border-b"></div>
        <FileTemplate />
        <div className="mt-[40px] border-[#EBEBEB] border-b"></div>
        <div className="flex items-center mt-[38px]">
          <div className="min-w-40 font-bold text-[#333] text-[14px]">{t('version')}</div>
          <div className="flex items-center gap-x-6">
            <span className="text-[#333] text-[14px]">V{version}</span>
            <div
              onClick={async () => {
                await openUrl(DOWNLOAD_URL);
              }}
              className="flex items-center gap-x-[6px] px-3 border border-[#EBEBEB] rounded-[8px] h-[28px] cursor-pointer"
            >
              <UpdateIcon />
              <span className="text-[#333] text-[12px]">{t('newVersion')}</span>
            </div>
          </div>
        </div>
        <div className="mt-[40px] border-[#EBEBEB] border-b"></div>
        <div className="flex items-center mt-4">
          <div className="min-w-40 font-bold text-[#333] text-[14px]">{t('contactUs')}</div>
          <div className="flex items-center gap-[6px]">
            <EmailIcon className="w-[17px] aspect-auto" />
            <span className="text-[#333] text-[14px]"><EMAIL></span>
          </div>
        </div>
      </div>
    </div>
  );
}
