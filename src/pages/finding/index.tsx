import { Image } from 'antd';
import { useTranslation } from 'react-i18next';
const Finding = () => {
  const { t } = useTranslation();
  return (
    <div className="flex flex-col pr-3 pb-3 h-full">
      <div className="bg-white px-[84px] pb-[50px] rounded-xl h-full text-[#333]">
        <div className="mx-auto mt-[60px] max-w-[700px]">
          <div className="font-bold text-[20px]">{t('comingsoon')}</div>
          <div className="mt-[14px] text-[14px]">{t('comingsoonDesc')}</div>
          <div className="bg-[#FAFAFA] mt-[40px] border border-[#EBEBEB] rounded-xl w-full">
            <Image src={'./finding.png'} className="aspect-auto" preview={false} />
          </div>
          <div className="flex flex-col justify-center gap-y-5 mt-[40px] px-28 text-[14px]">
            <div>
              <span>{t('featureTellUs')}</span>
              <span className="text-[#F98E47]"> <EMAIL></span>
            </div>
            <div>{t('lookforward')}</div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Finding;
