import { ConfigProvider, theme } from 'antd';
import { useTranslation } from 'react-i18next';

interface AntdProviderProps {
  children: React.ReactNode;
}

export default function AntdProvider({ children }: AntdProviderProps) {
  const { i18n } = useTranslation();

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          colorPrimary: '#3b82f6',
          colorSuccess: '#10b981',
          colorWarning: '#f59e0b',
          colorError: '#ef4444',
          colorInfo: '#3b82f6',
          borderRadius: 6,
          wireframe: false,
        },
        components: {
          Button: {
            borderRadius: 6,
            controlHeight: 36,
          },
          Card: {
            borderRadius: 8,
          },
          Input: {
            borderRadius: 6,
            controlHeight: 36,
          },
          Select: {
            borderRadius: 6,
            controlHeight: 36,
          },
          Table: {
            borderRadius: 8,
          },
          Modal: {
            borderRadius: 8,
          },
          Drawer: {
            borderRadius: 8,
          },
        },
      }}
      locale={{
        locale: i18n.language === 'zh' ? 'zh_CN' : 'en_US',
      }}
    >
      {children}
    </ConfigProvider>
  );
}
