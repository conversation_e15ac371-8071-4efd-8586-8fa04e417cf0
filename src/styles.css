@tailwind base;
@tailwind components;
@tailwind utilities;

.drag {
  -webkit-app-region: drag;
}
.no-drag {
  -webkit-app-region: no-drag;
}
html,
body,
#root {
  overscroll-behavior: none;
  /* 兼容性更好时可加 */
  overscroll-behavior-y: none;
}

/* Window border radius */
#root {
  border-radius: 12px;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
  background: white;
}

/* Ensure content doesn't overflow rounded corners */
body {
  overflow: hidden;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
}
/* 选中标签的文字颜色 */
.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #000 !important;
}

/* 选中标签下方的指示条颜色 */
.ant-tabs-tab:hover .ant-tabs-ink-bar {
  background: #000 !important; /* 你想要的颜色 */
}

.processing-row-waiting > td {
  background: rgba(243, 244, 246, 1) !important;
}
.processing-row-processing > td {
  background: rgba(255, 183, 0, 0.21) !important;
}
.processing-row-finished > td {
  background: #cee3cb !important;
}

.task-row > td {
  height: 56px;
  padding-block: 0px !important;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
