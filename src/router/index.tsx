import { createBrowserRouter } from 'react-router-dom';
import <PERSON><PERSON><PERSON><PERSON> from '@/pages/ai-rename';
import AIClassify from '@/pages/ai-classify';
import Settings from '@/pages/settings';
import Tasks from '@/pages/tasks';
import DefaultPage from '@/pages/default-page';
import StartPage from '@/pages/launch';
import FindingPage from '@/pages/finding';
const routers = [
  {
    path: '/',
    element: <DefaultPage />,
  },
  {
    path: 'rename',
    element: <AIRename />,
  },
  {
    path: 'classify',
    element: <AIClassify />,
  },
  {
    path: 'setting',
    element: <Settings />,
  },
  {
    path: 'tasks/:tabKey',
    element: <Tasks />,
  },
  {
    path: 'start',
    element: <StartPage />,
  },
  {
    path: 'finding',
    element: <FindingPage />,
  },
];

export const routerList = routers;
export const router = createBrowserRouter(routers);
