export interface FileItem {
  path: string;
  name: string;
  rStatus: 'Waiting' | 'Finished' | 'Processing' | 'Failed' | 'ApplyFinished' | 'ToApply';
  cStatus: 'Waiting' | 'Finished' | 'Processing' | 'Failed' | 'ApplyFinished' | 'ToApply';
  renaming?: string;
  classifying?: string;
  msg?: string;
  progress?: number;
  id?: string;
  size?: number;
  type?: string;
}

// 任务状态
export interface TaskState {
  files: FileItem[];
  pendingFiles: FileItem[];
  currentTaskMode: string;
  applied: boolean;
  renameFilesSameTime: boolean;
  classifyFilesSameTime: boolean;
  directoryPath: string;
  editingRowPath: string | '';
  formatList: { tag: string; zhCn: boolean }[];
}
