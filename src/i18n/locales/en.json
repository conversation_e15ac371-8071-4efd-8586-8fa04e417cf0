{"title": "WisFile", "welcome": "Welcome to WisFile", "menu": {"file": "File", "edit": "Edit", "view": "View", "help": "Help"}, "buttons": {"minimize": "Minimize", "maximize": "Maximize", "close": "Close"}, "OK": "OK", "Cancel": "Cancel", "language": "Interface Language", "autoApply": "Auto Apply AI Results", "version": "Version", "newVersion": "Update", "aboutUs": "About Us", "visitWebsite": "Visit Website", "feedback": "<PERSON><PERSON><PERSON>", "followSystem": "Follow System", "setting": "Settings", "autoClassify": "AI Foldering", "classifyRules": "Classification Rules", "supportedFormats": "Supported Formats", "namingRules": "Naming Rules", "renameFiles": "AI Renaming", "renameRules": "AI Rename Rules", "dragFiles": "Drag or click to select files", "dragFilesSimple": "Drag/click to add files ", "selectFolder": "Target Directory", "selectFile": "<PERSON><PERSON>", "fileWillBeClassified": "Files will be classified to directory:", "folderMustHaveClassified": "This directory must have existing classification directories for AI to determine where to place files.", "supportedFiles": "Supported files detected", "start": "Start", "unit": "files", "noSubFolders": "No subdirectories found in selected directory. Please create classification directories first.", "noFilesToProcess": "No files to process", "applySuccess": "Applied successfully", "FilesRenamed": "files renamed", "FilesClassified": "files classified", "maxFiles": "Maximum supported file count (500) reached", "reStart": "New", "openFolder": "Open Directory", "aiAnalysis": "AI analysis in progress, please wait for results", "back": "Back", "delete": "Delete", "noFiles": "No files", "waiting": "Waiting", "processing": "Processing", "success": "Success", "applyFailed": "Apply failed", "failed": "Failed", "status": "Status", "fileName": "File Name", "rename": "<PERSON><PERSON>", "classifyDir": "Classification Directory", "operation": "Operation", "undoRename": "<PERSON><PERSON>", "regenerate": "Regenerate", "moveDirectory": "Move Directory", "aiRename": "AI Rename", "clearAll": "Clear All", "tip": "Tip", "confirmOperation": "Are you sure you want to apply these operations?", "taskCompleted": "Task completed", "checkRenameClassify": "Please check rename or classify options", "storagePermission": "Please manually grant disk access permission", "apply": "Apply", "other": "Other", "exportLog": "Export Log", "currentInputText": "Current input text is", "zh": "中文", "en": "English", "systemLanguage": "Follow System", "aiReadTitle": "AI reads document title or header content to generate filename.", "aiReadContent": "AI reads document header content, can only classify into specified category directories, unclassifiable files go to \"Other\"", "requestCancelled": "Request cancelled", "connectionTimeout": "Connection timeout", "requestTimeout": "Request timeout", "responseTimeout": "Response timeout", "syntaxError": "Request syntax error", "reloginRequired": "Please login again", "serverRefused": "Server refused to execute", "serverUnreachable": "Cannot connect to server", "methodForbidden": "Request method forbidden", "serverError": "Server internal error", "invalidRequest": "Invalid request", "serverDown": "Server is down", "httpNotSupported": "HTTP protocol not supported", "unknownError": "Unknown error", "networkError": "Network connection error", "confirmCancel": "Are you sure you want to discard this AI analysis result?", "parseError": "Parse error", "directoryNoPermission": "Directory has no read/write permission", "directoryAccessError": "Directory access error", "selectFolderError": "Error selecting file", "renameError": "<PERSON><PERSON> failed", "pleaseSelect": "Please select", "downloaded": "Downloaded", "notDownloaded": "Not downloaded", "fileAlreadyExist": "File already exists", "aiRenameModel": "AI Rename Model", "aiClassifyModel": "AI Classification Model", "startService": "Starting service...", "downloadFailed": "Download failed, please check folder permissions", "confirmCloseApp": "Are you sure you want to close the application?", "appIsLoading": "Application is loading, please wait...", "grantDiskPermission": "Grant Full Disk Access", "grantDiskPermissionTip": "Please manually grant disk access permission", "grantDiskPermissionSuccess": "Disk access permission granted", "grantDiskPermissionFailed": "Failed to grant disk access permission", "onlineModel": "Online Model", "offlineModel": "Offline Model", "pleaseStopCurrentTask": "Please stop current task first", "lowLossModel": "Standard", "nonDestructiveModel": "Plus", "highLossModel": "Mini", "noInternetConnection": "No internet connection", "supportFormats": "We support only PDF and docx formats.", "uploadWordFile": "Please upload a WORD document", "uploadFileSizeError": "Please upload a document no larger than 5MB", "classifyFilesSameTime": "Classify these files at the same time", "renameFilesSameTime": "Rename these files at the same time", "chooseNamingFormat": "Naming Format", "clickToInsert": "Click tags by order", "formatPreview": "Preview", "iWantThis": "Confirm", "reset": "Reset", "chooseTargetDirectory": "Target directory", "modelSelection": "Model Selection", "renaming": "AI Renaming", "filesClassification": "AI Foldering", "contactUs": "Contact Us", "downloadModel": "Download Model", "selectedFiles": "Selected Files", "supportRenameFormats": "We support renaming in PDF formats", "unsupportedFileType": "is not a supported file type. Only PDF files are allowed.", "serviceStarting": "Service Starting...", "serviceStarted": "Service On", "serviceStopped": "Service Off", "startProcessing": "Start processing files", "confirmCloseTab": "Are you sure you want to close this tab?", "pleaseStartService": "Click here to switch models", "home": "Home", "downloading": "Downloading", "downloadingDesc": "Please wait...", "switchModelFailed": "Switch model failed", "switchModelSuccess": "Switch model success", "moveFailed": "Move failed", "renameFailed": "<PERSON><PERSON> failed", "deleteModelTitle": "Are you sure to delete this model?", "deleteModelContent": "Please redownload it while you want to use this model.", "deleteModelSuccess": "Delete model successfully", "deleteModelFailed": "Delete model failed", "selectDirectory": "Select directory", "applySuccessCount": "Successfully Applied {{count}} files", "applyPartialSuccess": "Successfully Applied {{success}},Failed to Apply {{failed}}", "editFormat": "Create My own format", "cancel": "Cancel", "smartRenaming": "Smart Renaming, Clean Folders", "clickSidebar": "Click on the sidebar", "startWisfile": "Start your Wisfile journey", "toApply": "To Apply", "toProtect": "To protect your privacy, you can:\n\nDownload <1>Offline AI Models</1> , enjoy functions of file renaming and foldering without any risk of data leaking.", "download": "Download", "skip": "Skip for now", "DownloadOfflineModels": "Download Offline Models", "tidyYourFilesWithAI": "Let AI Rename your messy Files", "aiSortfiles": "Let AI sort your files", "comingsoon": "AI Finding is coming soon!", "comingsoonDesc": "This feature can help you locate your files easily and intelligently.", "featureTellUs": "\nThis feature can help you locate your files easily and intelligently.\nInterested in this feature? Please tell us your needs through", "lookforward": "Looking forward to hearing your voice.", "finding": "AI Finding", "classify": "Folder", "FYI": "FYI", "reportIssueDesc": "Reporting issues means you  agree to upload the your issues, which enables us to resolve your issues and helps us further inprove Wisfile.", "pleaseEnterEmail": "Please leave your email address so that we can reach out to you.", "thinking": "Think Again", "reportIssue": "I Agree", "thingking": "Think Again", "thankYou": "Close", "thankyouForyour": "Thank you for your responds.", "issueReport": "Issue Report", "retry": "Retry", "DragAndDropFilesHere": "Drag and drop files here.", "delTemplateTitle": "Sure  to delete this format?", "confirm": "Confirm", "newTemplate": " +New", "weAcceleratingDownload": "We are accelerating the download...", "standardDesc": "Optimization strategies，high performance", "miniDesc": "Cost - effectiveness，fastest operation", "offlineModeOnlineDisabled": "Please turn off flight mode first", "welcometoWisfile": "Welcome to Wisfile", "welcometowisfiledescription": "You can use Wisfile to organise your local files in order, more importantly, you can achieve these functions completely offline and free!", "chooseYourMode": "Please choose your mode", "onlineTips": "In Privacy Mode, the software will operate without an internet connection. ", "offlineMode": "Privacy Mode", "onlineMode": "Online Mode", "chooseModels": "Please choose the offline models", "nextStep": "Next", "offlineModeOpen": "Privacy Mode on", "onlineModeClose": "Privacy Mode off", "pleaseDownloadModels": "Please Download and Switch to Offline Model first", "pleaseSwitchOfflineModels": "Please Switch to Offline Model first", "inPrivacyModeSomeOperations": "In Privacy Mode, some operations (like deleting or downloading models) are temporarily unavailable.", "modelsDownloadComplete": "Models download complete", "analyzingTargetDirectoryStructure": "Analyzing the target directory structure", "analyzingSubfolderAttributes": "Analyzing sub-folder attributes", "analyzingFileContent": "Parsing the file content", "extractingFileFeatures": "Extracting file features", "weParsingDirectory": "We are parsing the directory", "directoryParsingCompleted": "Directory parsing is completed", "directorySelectedTips": "Directory selected the 1st time or structure changed, Wisfile will auto-detect and parse the directory", "pleaseSwitchOnlineMode": "\nPlease Switch to Online Mode first", "Date": "Date", "Version": "Version Number", "Company": "Company Name", "Author": "Author", "ArticleTitle": "Article Title", "ShortTitle": "Article Short Title", "Conference": "Conference Name"}