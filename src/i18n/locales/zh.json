{"title": "智慧文件", "welcome": "欢迎使用智慧文件", "menu": {"file": "文件", "edit": "编辑", "view": "视图", "help": "帮助"}, "buttons": {"minimize": "最小化", "maximize": "最大化", "close": "关闭"}, "OK": "确定", "Cancel": "取消", "language": "界面语言", "autoApply": "自动应用AI处理结果", "version": "版本号", "newVersion": "更新", "aboutUs": "关于我们", "visitWebsite": "访问官网", "feedback": "意见反馈", "followSystem": "跟随系统", "setting": "设置", "autoClassify": "AI 文件自动归类", "classifyRules": "归类规则", "supportedFormats": "支持的格式", "namingRules": "命名规则", "renameFiles": "AI 文件重命名", "renameRules": "AI 重命名规则", "dragFiles": "拖入或点击选择文件", "dragFilesSimple": "拖入/点击添加文件", "selectFolder": "要归入的目录", "selectFile": "选择", "fileWillBeClassified": "文件将被归类到目录：", "folderMustHaveClassified": "该目录下要有已创建好的分类目录，方便AI判断归入相应目录。", "supportedFiles": "已检测到支持的文件", "start": "开始", "unit": "个", "noSubFolders": "所选目录没有任何子分类目录，请创建好分类目录再选择。", "noFilesToProcess": "没有待处理的文件", "applySuccess": "应用成功", "FilesRenamed": "个文件已重命名", "FilesClassified": "个文件已归类", "maxFiles": "已达到最大支持的文件数量500个", "reStart": "新建任务", "openFolder": "打开目录", "aiAnalysis": "AI分析中，请等待处理结果", "back": "返回", "delete": "删除", "noFiles": "暂无文件", "waiting": "等待处理", "processing": "处理中", "success": "处理成功", "applyFailed": "应用失败", "failed": "处理失败", "status": "状态", "fileName": "文件名", "rename": "重命名", "classifyDir": "归类目录", "operation": "操作", "undoRename": "撤销重命名", "regenerate": "重新生成", "moveDirectory": "移动目录", "aiRename": "AI改文件名", "clearAll": "全部清除", "tip": "提示", "confirmOperation": "确定要开始应用以上操作吗？", "taskCompleted": "任务已完成", "checkRenameClassify": "请勾选重命名或归类选项", "storagePermission": "请手动授予磁盘访问权限", "apply": "应用", "other": "其他", "exportLog": "导出日志", "currentInputText": "当前输入的文本是", "zh": "中文", "en": "English", "systemLanguage": "跟随系统", "aiReadTitle": "AI读取文档内标题或是头部内容生成标题作为文件名。", "aiReadContent": "AI读取文档头部内容，只能归类到指定的类别目录当中，无法分类的归入\"其他\"当中", "requestCancelled": "请求取消", "connectionTimeout": "连接超时", "requestTimeout": "请求超时", "responseTimeout": "响应超时", "syntaxError": "请求语法错误", "reloginRequired": "请重新登录", "serverRefused": "服务器拒绝执行", "serverUnreachable": "无法连接服务器", "methodForbidden": "请求方法被禁止", "serverError": "服务器内部错误", "invalidRequest": "无效的请求", "serverDown": "服务器挂了", "httpNotSupported": "不支持HTTP协议请求", "unknownError": "未知错误", "networkError": "网络连接异常", "confirmCancel": "你确认要放弃此次AI分析的结果吗？", "parseError": "解析错误", "directoryNoPermission": "目录没有读写权限", "directoryAccessError": "目录访问错误", "selectFolderError": "选择文件时出错", "renameError": "重命名失败", "pleaseSelect": "请选择", "downloaded": "已下载", "notDownloaded": "未下载", "fileAlreadyExist": "文件已存在", "aiRenameModel": "AI重命名模型", "aiClassifyModel": "AI归类模型", "startService": "启动服务中...", "downloadFailed": "下载失败，请检查文件夹权限", "confirmCloseApp": "确定要关闭应用吗？", "appIsLoading": "应用正在加载中，请稍后...", "grantDiskPermission": "授予磁盘完全访问权限", "grantDiskPermissionTip": "请手动授予磁盘访问权限", "grantDiskPermissionSuccess": "磁盘访问权限已授予", "grantDiskPermissionFailed": "磁盘访问权限授予失败", "onlineModel": "在线模型", "offlineModel": "离线模型", "pleaseStopCurrentTask": "请先停止当前任务", "lowLossModel": "增强版", "nonDestructiveModel": "完全版", "highLossModel": "高效版", "noInternetConnection": "没有网络连接", "supportFormats": "我们仅支持PDF 和 docx 格式。", "uploadWordFile": "请上传WORD文档", "uploadFileSizeError": "请上传大小不超过5M的文档", "classifyFilesSameTime": "同时归类这些文件", "renameFilesSameTime": "同时重命名这些文件", "chooseNamingFormat": "文件命名格式", "clickToInsert": "按照命名顺序选择标签", "formatPreview": "效果预览", "iWantThis": "确定", "reset": "重置", "chooseTargetDirectory": "选择目录", "modelSelection": "模型选择", "renaming": "重命名", "filesClassification": "文件分类", "contactUs": "联系我们", "downloadModel": "下载模型", "selectedFiles": "已选择文件", "supportRenameFormats": "我们支持PDF 格式文件的重命名。", "serviceStarting": "服务正在启动中...", "serviceStarted": "服务运行中", "serviceStopped": "服务已停止", "startProcessing": "开始处理文件", "confirmCloseTab": "确定要关闭此标签页吗？", "pleaseStartService": "点击这里切换模型", "unsupportedFileType": "不支持的文件， 仅支持PDF", "home": "首页", "downloading": "下载中", "downloadingDesc": "请稍后...", "switchModelFailed": "切换模型失败", "switchModelSuccess": "切换模型成功", "moveFailed": "移动失败", "renameFailed": "重命名失败", "deleteModelTitle": "确定删除模型吗？", "deleteModelContent": "想要使用此模型，请重新下载", "deleteModelSuccess": "删除模型成功", "deleteModelFailed": "删除模型失败", "selectDirectory": "选择要移入的目录", "applySuccessCount": "成功处理 {{count}} 个文件", "applyPartialSuccess": "成功 {{success}} 个, 失败 {{failed}} 个", "editFormat": "创建我的命名格式", "cancel": "取消", "smartRenaming": "智能命名，自动归类", "clickSidebar": "点击侧边栏", "startWisfile": "开始你的 Wisfile 智能整理之旅", "toApply": "待应用", "selectModelQuality": "选择模型类型", "toProtect": "To protect your privacy, you can:\n\nDownload <1>Offline AI Models</1> , enjoy functions of file renaming and foldering without any risk of data leaking.", "download": "Download", "skip": "Skip for now", "tidyYourFilesWithAI": "让 AI 为你的文件 “正” 名", "aiSortfiles": "让 AI 为你的文件 “整” 装", "finding": "AI 文件智能查找", "comingsoon": "AI文件查找功能即将上线！", "comingsoonDesc": "它能帮助你快速的找到目标文件。", "featureTellUs": "如果你对这个功能感兴趣，欢迎发送邮件到", "lookforward": "我们期待倾听您的声音。", "classify": "文件归类", "FYI": "温馨提示", "reportIssueDesc": "同意问题上报即表示您同意将您遇到文件处理错误情况反馈给我们，这对我们解决问题，并进一步优化Wisfile有很大帮助。", "pleaseEnterEmail": "请留下您的邮箱地址以便我们与您取得联系。", "thinking": "我再想想", "reportIssue": "同意上传", "thingking": "我再想想", "thankYou": "关闭", "thankyouForyour": "感谢您的反馈", "issueReport": "问题上报", "retry": "重试", "DragAndDropFilesHere": "在此处拖放文件", "delTemplateTitle": "确定删除这个格式吗？", "confirm": "确认", "newTemplate": "新建", "DownloadOfflineModels": "Download Offline Models", "weAcceleratingDownload": "我们正在加速下载……", "standardDesc": "适配高性能，采用优化策略", "miniDesc": "兼顾性价比，实现最快运行", "offlineModeOnlineDisabled": "请先切换至在线模式", "welcometoWisfile": "欢迎来到Wisfile", "welcometowisfiledescription": "Wisfile可以帮助你整理本地文件。你甚至可以完全离线且免费地使用我们的功能。", "chooseYourMode": "请选择你的使用模式", "onlineTips": "隐私模式下，软件将不连接互联网地实现其功能。", "offlineMode": "隐私模式", "onlineMode": "在线模式", "chooseModels": "请选择离线模型", "nextStep": "下一步", "offlineModeOpen": "隐私模式开启", "onlineModeClose": "隐私模式关闭", "pleaseDownloadModels": "请先下载离线模型后切换", "pleaseSwitchOfflineModels": "请先更换至离线模型后切换", "inPrivacyModeSomeOperations": "隐私模式下，部分操作（如模型的删除、下载等）暂不可用。", "modelsDownloadComplete": "模型下载完成", "analyzingTargetDirectoryStructure": "解析目标目录结构", "analyzingSubfolderAttributes": "分析子文件夹属性", "analyzingFileContent": "解析文件内容", "extractingFileFeatures": "抽取文件特征", "weParsingDirectory": "我们正在解析目录", "directoryParsingCompleted": "- 目录解析完成", "directorySelectedTips": "首次选择目录/目录结构更改时，将自动检测并解析目录", "pleaseSwitchOnlineMode": "请先切换至在线模式", "Date": "发布日期", "Version": "版本号", "Company": "公司名", "Author": "作者", "ArticleTitle": "文章标题", "ShortTitle": "文章简称", "Conference": "会议名"}