// 字符串工具类
import { platform } from '@tauri-apps/plugin-os';

// Platform detection utility
export const getPlatformType = (): 'mac' | 'win' | 'linux' => {
  const p = platform();
  if (p === 'macos') return 'mac';
  else if (p === 'windows') return 'win';
  else return 'linux';
};

export const getFileName = (path: string) => {
  if (!path) {
    return '';
  }

  // Support both Windows (\) and macOS/Linux (/) path separators
  // Use regex to match content after the last path separator
  const match = path.match(/[^/\\]+$/);
  return match ? match[0] : '';
};

export const getFileExtension = (path: string) => {
  if (!path) {
    return '';
  }
  return path.split('.').pop() || '';
};

export const getFileNameWithoutExtension = (path: string) => {
  if (!path) {
    return '';
  }
  return path.split('.').slice(0, -1).join('.') || '';
};

// 获取简短的文件路径 xxx...xxx
export const getShortFilePath = (path: string) => {
  if (!path) {
    return '';
  }
  if (path.length <= 40) {
    return path;
  }
  return path.split('/').slice(0, -1).join('/') + '...' + path.split('/').pop() || '';
};

// 获取短的文件名 如 xxx...xxx.png
export const getShortFileName = (name: string) => {
  if (!name) {
    return '';
  }
  if (name.length <= 10) {
    return name;
  }
  return name.slice(0, 3) + '...' + name.slice(-3);
};
