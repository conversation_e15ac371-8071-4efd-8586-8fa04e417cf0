import { Store } from '@tauri-apps/plugin-store';

/**
 * Tauri Store local storage utility class
 * Supports multiple instances, type inference, common method encapsulation, and version management
 */
export class TauriStore {
  private store: Store;
  private static _instance: TauriStore | null = null;
  private static readonly VERSION_KEY = '__app_version__';

  private constructor(store: Store) {
    this.store = store;
  }

  /**
   * Factory method: Asynchronously create Store instance (singleton)
   * @param storeFile Storage file name, default 'app-store.dat'
   * @param currentVersion Current application version
   */
  static async create(
    storeFile: string = 'app-store.dat',
    currentVersion?: string
  ): Promise<TauriStore> {
    if (TauriStore._instance) {
      return TauriStore._instance;
    }

    const store = await Store.load(storeFile);
    TauriStore._instance = new TauriStore(store);

    // If version number is provided, perform version check
    if (currentVersion) {
      await TauriStore._instance.checkAndClearIfNeeded(currentVersion);
    }

    return TauriStore._instance;
  }

  /**
   * Check version and clear data if needed
   * @param currentVersion Current application version
   */
  private async checkAndClearIfNeeded(currentVersion: string): Promise<void> {
    try {
      const storedVersion = await this.get<string>(TauriStore.VERSION_KEY);

      // If version has never been stored, or current version is greater than stored version, clear data
      if (!storedVersion || this.isVersionGreater(currentVersion, storedVersion)) {
        // Clear Tauri Store data
        await this.clear();

        // Clear localStorage data
        if (typeof window !== 'undefined' && window.localStorage) {
          window.localStorage.clear();
        }

        // Clear sessionStorage data
        if (typeof window !== 'undefined' && window.sessionStorage) {
          window.sessionStorage.clear();
        }
      }

      // Update version number
      await this.set(TauriStore.VERSION_KEY, currentVersion);
    } catch (error) {
      console.error('Version check failed:', error);
    }
  }

  /**
   * Compare version numbers
   * @param version1 Version 1
   * @param version2 Version 2
   * @returns Returns true when version1 > version2
   */
  private isVersionGreater(version1: string, version2: string): boolean {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);

    const maxLength = Math.max(v1Parts.length, v2Parts.length);

    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;

      if (v1Part > v2Part) return true;
      if (v1Part < v2Part) return false;
    }

    return false;
  }

  /** Set key-value pair */
  async set<T = any>(key: string, value: T): Promise<void> {
    await this.store.set(key, value);
    await this.store.save();
  }

  /** Get value by key */
  async get<T = any>(key: string): Promise<T | undefined> {
    return await this.store.get<T>(key);
  }

  /** Delete key */
  async remove(key: string): Promise<void> {
    await this.store.delete(key);
    await this.store.save();
  }

  /** Clear all data */
  async clear(): Promise<void> {
    await this.store.clear();
    await this.store.save();
  }

  /** Get all keys */
  async keys(): Promise<string[]> {
    return await this.store.keys();
  }

  /**
   * Manually trigger version check and cleanup
   * @param currentVersion Current version
   */
  async manualVersionCheck(currentVersion: string): Promise<void> {
    await this.checkAndClearIfNeeded(currentVersion);
  }

  /**
   * Get current stored version number
   */
  async getCurrentStoredVersion(): Promise<string | undefined> {
    return await this.get<string>(TauriStore.VERSION_KEY);
  }
}

// Usage example:
// const store = await TauriStore.create('app-store.dat', '1.2.0');
// await store.set('token', '123');
// const token = await store.get<string>('token');
//
// // Manual version check
// await store.manualVersionCheck('1.2.1');
