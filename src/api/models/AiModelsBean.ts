import type { FileItem } from '@/types/file';

export interface AiModelsBean {
  name: string;
  full_name: string;
  sha256: string;
  size: number;
  type: string;
  status: string;
  labelName: string;
}

export interface AiModelsReqBean {
  name: string;
  sha256: string;
  size: number;
  type: string;
}

export interface DownloadResult {
  name: string;
  uuid: string;
  total: number;
}
export interface ProcessingStatus {
  uuid: string;
  status: StatusContent;
  total: number;
}
export interface StatusContent {
  type: string;
  content: number;
}

export interface ModelType {
  rename: {
    type: string;
    data?: AiModelsReqBean;
  };
  embedding: {
    type: string;
    data?: AiModelsReqBean;
  };
}
export interface RenameAndClassifyBean {
  source: string[];
  target?: string | null;
  onlyClassify: boolean;
  renameTags: { tag: string; zhCn: boolean }[];
}

export interface TaskParams {
  mode: string;
  files: FileItem[];
  renameFilesSameTime: boolean;
  classifyFilesSameTime: boolean;
  directoryPath: string;
  formatList: { tag: string; zhCn: boolean }[];
  tabKey: string;
}

export interface EmbeddingDirsBean {
  target?: string | null;
}
