import { invoke } from '@tauri-apps/api/core';
import { info, error } from '@tauri-apps/plugin-log';

/**
 * 通用 Tauri invoke 封装，支持类型推断和统一错误处理
 * @param command Rust 后端命令名
 * @param args 传递参数对象
 * @returns Promise<T>
 */
async function tauriInvoke<T = any>(command: string, args?: Record<string, any>): Promise<T> {
  try {
    info(`[Tauri Invoke] ${command}: ${JSON.stringify(args)}`);
    const result = await invoke<T>(command, {
      request: args,
    });
    info(`[Tauri Invoke Success] ${command}: ${result}`);
    return result;
  } catch (err) {
    // 这里可以统一处理错误，比如弹窗、日志等
    error(`[Tauri Invoke Error] ${command}: ${err}`);
    throw err;
  }
}

export async function wisFileInvoke<T>(args: Record<string, any>): Promise<T> {
  try {
    const result = await tauriInvoke<string>('wisfile', {
      method: args.method,
      path: args.path,
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(args.body),
    });
    if (result) {
      return JSON.parse(result) as T;
    }
    return result as T;
  } catch (err) {
    error(`[Tauri Invoke Error] ${err}`);
    throw err;
  }
}

export async function servicesInvoke<T>(args: Record<string, any>): Promise<T> {
  try {
    const result = await tauriInvoke<string>('services', {
      path: args.path,
      headers: {
        'Content-Type': 'application/json',
      },
      method: args.method,
      body: JSON.stringify(args.body),
    });
    if (result) {
      return JSON.parse(result) as T;
    }
    return result as T;
  } catch (err) {
    error(`[Tauri Invoke Error] ${err}`);
    throw err;
  }
}
