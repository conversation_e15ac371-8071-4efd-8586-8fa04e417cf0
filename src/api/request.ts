// src/api/request.ts
import axios from 'axios';
import { logInfo, logError } from '@/utils/logger';

const instance = axios.create({
  baseURL: 'https://api.example.com',
  timeout: 10000,
});

// 请求拦截器
instance.interceptors.request.use(
  config => {
    // 这里可以加 token、日志等
    logInfo(`[Request] ${config}`);
    return config;
  },
  error => {
    logError(`[Request Error] ${error}`);
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    logInfo(`[Response] ${response}`);
    return response;
  },
  error => {
    logError(`[Response Error] ${error}`);
    return Promise.reject(error);
  }
);

export default instance;
