import {
  AiModelsBean,
  DownloadResult,
  EmbeddingDirsBean,
  ModelType,
  ProcessingStatus,
  RenameAndClassifyBean,
} from './models/AiModelsBean';
import { BaseResult } from './models/BaseResult';
import { servicesInvoke, wisFileInvoke } from './tauriInvoke';
import { invoke } from '@tauri-apps/api/core';
import { info, error } from '@tauri-apps/plugin-log';

const _base_url = '/api/v1';
const _base_url2 = '/v1/api';

export const getServiceStatus = async () => {
  return (
    (await invoke<boolean>('embedding_alive', {
      llamaMode: 'Rename',
    })) &&
    (await invoke<boolean>('embedding_alive', {
      llamaMode: 'Embedding',
    }))
  );
};

export const getModels = async () => {
  return await servicesInvoke<BaseResult<AiModelsBean[]>>({
    path: `${_base_url}/models/list`,
    method: 'POST',
    body: ['Rename', 'Embedding'],
  });
};

export const getModelsInstalled = async () => {
  return await servicesInvoke<BaseResult<AiModelsBean[]>>({
    path: `${_base_url}/models/installed/list`,
    method: 'GET',
    body: {},
  });
};

export const getDownloadProgress = async (uuids: string[]) => {
  return await servicesInvoke<BaseResult<ProcessingStatus[]>>({
    path: `${_base_url}/download/process`,
    method: 'POST',
    body: uuids,
  });
};

export const downloadModel = async (models: { name: string; type: string }[]) => {
  return await servicesInvoke<BaseResult<DownloadResult[]>>({
    path: `${_base_url}/models/download`,
    method: 'POST',
    body: models,
  });
};

export const removeModel = async (models: { name: string; type: string }[]) => {
  return await servicesInvoke<BaseResult<boolean>>({
    path: `${_base_url}/models/remove`,
    method: 'POST',
    body: { models, remove_cache: true },
  });
};

export const switchModel = async (modelType: ModelType) => {
  try {
    if (!modelType.rename.type || !modelType.embedding.type) {
      throw new Error('Error Switch Model');
    }
    // todo: Check model status and pass true/false
    info(`Switch model params: ${JSON.stringify(modelType)}`);
    const res = await invoke('set_model', {
      modelType,
      forceRestart: true,
    });
    return res;
  } catch (e) {
    error(`Switch model failed ${JSON.stringify(modelType)} ${e}`);
  }
};

// Get selected model
export const getSelectedModel = async () => {
  try {
    const result = await invoke<string>('get_model');
    return (result || {}) as ModelType;
  } catch (e) {
    error(`Get model failed ${e}`);
  }
};

export const renameAndClassify = async (req: RenameAndClassifyBean, onEvent: any) => {
  try {
    info(`Rename and classify params: ${JSON.stringify(req)}`);
    return await invoke('rename_and_classify', {
      source: req.source,
      onlyClassify: req.onlyClassify,
      renameTags: req.renameTags,
      target: req.target,
      onEvent,
    });
  } catch (e) {
    error(`Rename and classify failed ${req.source} -> ${req.target} ${e}`);
  }
};

// Separator line
export const watch = async (dir: string[]) => {
  return await wisFileInvoke<string>({
    path: `${_base_url2}/profile/watch`,
    method: 'POST',
    body: dir,
  });
};

export const unwatch = async (dir: string[]) => {
  return await wisFileInvoke<string>({
    path: `${_base_url2}/profile/unwatch`,
    method: 'POST',
    body: dir,
  });
};

// Rename file
export const renameFile = async (req: { source: string; target: string }) => {
  try {
    info(`Rename params: ${req.source} -> ${req.target}`);
    const res = await invoke('file_rename', {
      src: req.source,
      newName: req.target,
    });
    info(`Rename result: ${JSON.stringify(res)}`);
    return {
      success: true,
      msg: `${res}`,
    };
  } catch (e) {
    error(`Rename file failed ${req.source} -> ${req.target} ${e}`);
    return {
      success: false,
      msg: `${e}`,
    };
  }
};

// Move file
export const moveFile = async (req: { source: string; target: string }) => {
  try {
    info(`Move file params: ${req.source} -> ${req.target}`);
    const res = await invoke('mv_file', {
      src: req.source,
      target: req.target,
    });
    info(`Move result: ${JSON.stringify(res)}`);
    return {
      success: true,
      msg: `${res}`,
    };
  } catch (e) {
    error(`Move file failed ${req.source} -> ${req.target} ${e}`);
    return {
      success: false,
      msg: `${e}`,
    };
  }
};

export const getSysLanguage = async () => {
  try {
    const res = await invoke<string>('system_lang');
    if (res.includes('zh')) {
      return 'zh';
    } else {
      return 'en';
    }
  } catch (e) {
    error(`Get system language failed ${e}`);
    return 'en'; // Default to English
  }
};

// Create collection

export const createCollection = async (issueKind: string) => {
  try {
    info(`Create collection params: ${issueKind}`);
    const res = await invoke('create_collection', {
      issueKind,
    });
    info(`Create collection result: ${JSON.stringify(res)}`);
    return {
      success: true,
      msg: `${res}`,
    };
  } catch (e) {
    error(`Create collection failed ${issueKind} ${e}`);
    return {
      success: false,
      msg: `${e}`,
    };
  }
};

export const collectionUploadFile = async (
  sourcePath: string,
  collection: string,
  issueKind: string,
  email: string | null = null
) => {
  await invoke('upload_file', {
    sourcePath,
    collection,
    issueKind,
    email,
  });
};

export const uiEvent = async (event: string, props: any) => {
  try {
    info(`UI event params: ${event} - ${JSON.stringify(props)}`);
    await invoke('ui_event', {
      event,
      props,
    });
  } catch (e) {
    error(`UI event failed ${event} - ${JSON.stringify(props)} ${e}`);
  }
  return true;
};

export const getWisConfig = async () => {
  try {
    const res = await invoke<string>('get_wis_config');
    return JSON.parse(res);
  } catch (e) {
    error(`Get WisConfig failed ${e}`);
    return {};
  }
};

export const updateWisConfig = async (config: Record<string, any>) => {
  try {
    info(`Update WisConfig params: ${JSON.stringify(config)}`);
    const res = await invoke('update_wis_config', {
      config: JSON.stringify(config),
    });
    info(`Update WisConfig result: ${JSON.stringify(res)}`);
    return {
      success: true,
      msg: `${res}`,
    };
  } catch (e) {
    error(`Update WisConfig failed ${JSON.stringify(config)} ${e}`);
    return {
      success: false,
      msg: `${e}`,
    };
  }
};

export const embeddingDirs = async (req: EmbeddingDirsBean, onEvent: any) => {
  try {
    info(`Embedding params: ${JSON.stringify(req)}`);
    return await invoke('embedding_dirs', {
      target: req.target,
      onEvent,
    });
  } catch (e) {
    error(`Embedding dirs failed ${req.target} ${e}`);
  }
};
