import classNames from 'classnames';
import { Image } from 'antd';
import { useTranslation } from 'react-i18next';
import { useTabsContext } from '@tauri-apps/plugin-tabs';
import { uiEvent } from '@/api/api';

interface MenuItemProps {
  item: {
    key: string;
    title: string;
    event: string;
    icon: string;
  };
}

const MenuItem = (props: MenuItemProps) => {
  const { t } = useTranslation();
  const { item } = props;
  const { open, activeTabId } = useTabsContext();

  const onClickTab = (key: string, label: string, event: string) => {
    uiEvent('Click', { Function: [event] });

    open({ id: key, title: label, path: key });
  };

  return (
    <div
      onClick={() => onClickTab(item.key, item.title, item.event)}
      key={item.key}
      className={classNames(
        'flex gap-3 items-center pl-3 h-[42px] box-border border cursor-pointer',
        {
          'rounded-xl  border-[#FFDF75] bg-[#FFF9E5] font-bold': activeTabId === item.key,

          'border-transparent font-normal': activeTabId !== item.key,
        }
      )}
    >
      <Image src={`/${item.icon}`} preview={false} />
      <div>{t(item.title)}</div>
    </div>
  );
};

export default MenuItem;
