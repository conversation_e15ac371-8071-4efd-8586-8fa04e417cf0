import { Outlet } from 'react-router-dom';

import Menu from './menu';
import { useSelector } from 'react-redux';
import DragIcon from '@/assets/svg/drag.svg?react';
import { useTranslation } from 'react-i18next';
import FlightSwitch from '@/components/FlightSwitch';
import Embedding from '@/components/Embedding';
import { getPlatformType } from '@/utils/string';

const Layout = () => {
  const { t } = useTranslation();
  const isDragging = useSelector((state: any) => state.drag.isDragging);
  const os = getPlatformType();

  return (
    <div className="flex w-full h-[calc(100vh-42px)]">
      <Menu />
      {/* 只在 Mac 平台显示右上角的 FlightSwitch */}
      {os === 'mac' && <FlightSwitch />}
      <Embedding />
      <div className="relative ml-[250px] w-[calc(100vw-250px)]">
        {isDragging && (
          <div className="top-0 right-0 bottom-0 left-0 z-50 absolute flex flex-col justify-center items-center bg-[#FFF9E5] pb-[60px] border border-[#FFDF75] border-dashed rounded-lg">
            <DragIcon />
            <div className="mt-[24px] font-bold text-[#333] text-[20px]">
              {t('DragAndDropFilesHere')}
            </div>
            <div className="text-[#333] text-[10px]">{t('supportFormats')}</div>
          </div>
        )}
        <Outlet />
      </div>
    </div>
  );
};

export default Layout;
