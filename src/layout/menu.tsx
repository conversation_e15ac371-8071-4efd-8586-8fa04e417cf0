import { menuItems, settingsItem } from './var';
import MenuItem from './MenuItem';
import LogoIcon from '@/assets/svg/logo.svg?react';
import FlightSwitch from '@/components/FlightSwitch';
import { getPlatformType } from '@/utils/string';

const Menu = () => {
  const os = getPlatformType();

  return (
    <div
      id="menu"
      className="top-12 bottom-0 absolute flex flex-col justify-between px-3 pb-3 w-[250px]"
    >
      <div>
        <div className="flex items-center gap-x-3">
          <LogoIcon className="w-9 h-9"></LogoIcon>
          <span className="font-bold text-[#333] text-[20px]">WisFile</span>
        </div>
        <div className="flex flex-col gap-y-4 mt-8">
          {menuItems.map(item => {
            return <MenuItem item={item} key={item.key} />;
          })}
        </div>
      </div>
      <div className="flex flex-col">
        {/* 在 Windows 平台上显示隐私模式组件 */}
        {os === 'win' && <FlightSwitch isInSidebar={true} />}
        <MenuItem item={settingsItem} />
      </div>
    </div>
  );
};

export default Menu;
