import { useEffect, lazy, Suspense } from 'react';
import { useTasksStore } from './store/tasksStore';
import { TauriStore } from './utils/store';
import { LOCAL_LANGUAGE } from '@/const/language';
import { useTranslation } from 'react-i18next';
import { locale } from '@tauri-apps/plugin-os';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from './store';
import { fetchModels } from './store/slices/settings';
import { useAppInitialization } from './hooks/useAppInitialization';
import { useDataPersistence } from './hooks/useDataPersistence';
import { useDirectoryEmbedding } from './hooks/useDirectoryEmbedding';
import Flight from './components/Flight';
import { DIR_STORE_KEY, FLIGHT_STORE_KEY, TASK_STORE_KEY } from './const/tauriStore';
import { getVersion } from '@tauri-apps/api/app';

// 懒加载 TheHome 组件，避免在不需要时初始化 useNavigate
const TheHome = lazy(() => import('./pages/thehome'));

function App(): JSX.Element | null {
  const tasks = useTasksStore(state => state.tasks);
  const { i18n } = useTranslation();
  const flightData = useSelector((state: RootState) => state.flight.flightData);
  const dir = useSelector((state: RootState) => state.dir);
  const { directoryPath } = dir;
  const dispatchRTK = useDispatch<AppDispatch>();

  // 使用自定义 hooks
  const initialized = useAppInitialization();
  useDataPersistence(tasks, TASK_STORE_KEY, initialized);
  useDataPersistence(flightData, FLIGHT_STORE_KEY, initialized);
  useDataPersistence(dir.directoryPath, DIR_STORE_KEY, initialized);

  // 使用目录嵌入 hook
  useDirectoryEmbedding(directoryPath);

  useEffect(() => {
    dispatchRTK(fetchModels());
  }, [dispatchRTK]);

  useEffect(() => {
    (async () => {
      const sysLang = await locale();
      const store = await TauriStore.create();
      const localLang = await store.get(LOCAL_LANGUAGE);

      const currentVersion = await getVersion();
      await store.manualVersionCheck(currentVersion);

      if (!localLang && sysLang) {
        await store.set(LOCAL_LANGUAGE, sysLang.includes('en') ? 'en' : 'zh');
        i18n.changeLanguage(sysLang);
      }
      if (localLang) {
        i18n.changeLanguage(localLang);
      }
    })();
  }, [i18n]);

  if (!initialized) return null;

  if (flightData.step !== 3) {
    return (
      <div className="flex justify-center items-center h-screen overflow-auto">
        <Flight />
      </div>
    );
  }

  return (
    <div className="flex flex-col bg-[#FFFBEF] w-full">
      <Suspense fallback={<div>Loading...</div>}>
        <TheHome />
      </Suspense>
    </div>
  );
}

export default App;
