// DBContext.tsx
import React, { ReactNode } from 'react';
import { add, get, update, del } from './';
import { DBContext } from './DBContext';

interface DBProviderProps {
  children: ReactNode;
}

export const DBProvider: React.FC<DBProviderProps> = ({ children }) => {
  const addTask = async (id: string, content: string) => {
    await add(id, content);
  };

  const getTask = async (id: string) => {
    const task = await get(id);
    return task;
  };

  const updateTask = async (id: string, content: string) => {
    await update(id, content);
  };

  const deleteTask = async (id: string) => {
    await del(id);
  };

  return (
    <DBContext.Provider value={{ addTask, getTask, updateTask, deleteTask }}>
      {children}
    </DBContext.Provider>
  );
};
