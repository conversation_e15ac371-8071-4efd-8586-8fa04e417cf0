import { openDB, IDBPDatabase } from 'idb';

const DATABASE_NAME = 'database';
const Task = 'task';

let dbInstance: IDBPDatabase | null = null;

// 初始化数据库
async function initDB(): Promise<IDBPDatabase> {
  if (!dbInstance) {
    dbInstance = await openDB(DATABASE_NAME, 1, {
      upgrade(db) {
        if (!db.objectStoreNames.contains(Task)) {
          db.createObjectStore(Task, { keyPath: 'id' });
        }
      },
    });
  }
  return dbInstance;
}

// 插入数据
async function add(id: string, content: string): Promise<void> {
  const db = await initDB();
  const tx = db.transaction(Task, 'readwrite');
  const store = tx.objectStore(Task);
  await store.put({ id, content });
  await tx.done;
}

async function get(id: string): Promise<{ id: string; content: string } | undefined> {
  const db = await initDB();
  const tx = db.transaction(Task, 'readonly');
  const store = tx.objectStore(Task);
  const friend = await store.get(id);
  await tx.done;
  return friend;
}

// 更新数据
async function update(id: string, content: string): Promise<void> {
  const db = await initDB();
  const tx = db.transaction(Task, 'readwrite');
  const store = tx.objectStore(Task);
  const data = await store.get(id);
  if (data) {
    data.content = content;
    await store.put(data);
  }
  await tx.done;
}

// 删除数据
async function del(id: string): Promise<void> {
  const db = await initDB();
  const tx = db.transaction(Task, 'readwrite');
  const store = tx.objectStore(Task);
  await store.delete(id);
  await tx.done;
}

export { add, get, update, del };
