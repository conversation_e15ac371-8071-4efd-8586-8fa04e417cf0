import { createContext, useContext } from 'react';

interface Task {
  id: string;
  content: string;
}

interface DBContextType {
  addTask: (id: string, content: string) => Promise<void>;
  getTask: (id: string) => Promise<Task | undefined>;
  updateTask: (id: string, content: string) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;
}

// 创建 Context
export const DBContext = createContext<DBContextType | undefined>(undefined);

export const useDB = (): DBContextType => {
  const context = useContext(DBContext);
  if (!context) {
    throw new Error('useDB must be used within a DBProvider');
  }
  return context;
};
