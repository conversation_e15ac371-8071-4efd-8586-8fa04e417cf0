import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Channel } from '@tauri-apps/api/core';
import { embeddingDirs } from '@/api/api';
import { RenameClassify } from '@/pages/tasks/type';
import { dirActions } from '@/store/slices/dir';
import { AppDispatch } from '@/store';

/**
 * 自定义 Hook 用于处理目录嵌入
 * @param directoryPath 目录路径
 */
export const useDirectoryEmbedding = (directoryPath: string | null): void => {
  const dispatchRTK = useDispatch<AppDispatch>();

  useEffect(() => {
    if (!directoryPath) return;

    const onEvent = new Channel<RenameClassify>();
    let isSubscribed = true;

    const handleClassifyProcess = (message: RenameClassify) => {
      if (!isSubscribed || message.event !== 'classifyProcess') return;

      const data = message.data || {};
      const src = Number(data.src) || 0;
      const total = Number(data.total) || 0;

      if (total <= 0) {
        if (src === 0 && total === 0) {
          dispatchRTK(
            dirActions.updateEmbeddingData({
              status: 'success',
              progress: 100,
            })
          );
        }
        return;
      }

      const progress = Math.min(100, Math.floor((src / total) * 100));
      const status = src >= total ? 'success' : 'processing';

      dispatchRTK(
        dirActions.updateEmbeddingData({
          status,
          progress,
          num: src,
          total,
        })
      );
    };

    onEvent.onmessage = handleClassifyProcess;

    dispatchRTK(
      dirActions.updateEmbeddingData({
        status: 'processing',
        progress: 0,
      })
    );

    embeddingDirs(
      {
        target: directoryPath,
      },
      onEvent
    ).catch(error => {
      console.error('嵌入目录处理失败:', error);
      if (isSubscribed) {
        dispatchRTK(
          dirActions.updateEmbeddingData({
            status: 'error',
            progress: 0,
          })
        );
      }
    });

    return () => {
      isSubscribed = false;
    };
  }, [directoryPath, dispatchRTK]);
};
