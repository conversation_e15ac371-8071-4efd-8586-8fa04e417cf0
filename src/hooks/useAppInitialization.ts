import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { TauriStore } from '@/utils/store';
import { useTasksStore } from '@/store/tasksStore';
import { flightActions } from '@/store/slices/flight';
import { AppDispatch } from '@/store';
import { dirActions } from '@/store/slices/dir';
import { DIR_STORE_KEY, FLIGHT_STORE_KEY, TASK_STORE_KEY } from '@/const/tauriStore';

export const useAppInitialization = (): boolean => {
  const [initialized, setInitialized] = useState(false);
  const dispatchRTK = useDispatch<AppDispatch>();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        const store = await TauriStore.create();

        const tasksStr = await store.get(TASK_STORE_KEY);
        if (tasksStr) {
          const tasks = JSON.parse(tasksStr);
          useTasksStore.setState({ tasks });
        } else {
          useTasksStore.setState({ tasks: {} });
        }

        const flightDataStr = await store.get(FLIGHT_STORE_KEY);
        if (flightDataStr) {
          const flightData = JSON.parse(flightDataStr);
          dispatchRTK(flightActions.updateFlightData(flightData));
        }

        const dirStore = await store.get(DIR_STORE_KEY);
        if (dirStore) {
          const dirData = JSON.parse(dirStore);
          dispatchRTK(dirActions.updateDirectoryPath(dirData));
        }

        setInitialized(true);
      } catch (error) {
        console.error('Failed to load data:', error);
        setInitialized(true);
      }
    };

    initializeApp();
  }, [dispatchRTK]);

  return initialized;
};
