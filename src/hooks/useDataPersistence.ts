import { useEffect } from 'react';
import { TauriStore } from '@/utils/store';

export const useDataPersistence = <T>(data: T, key: string, initialized: boolean): void => {
  useEffect(() => {
    if (data && initialized) {
      const dataStr = JSON.stringify(data);
      TauriStore.create()
        .then(store => {
          return store.set(key, dataStr);
        })
        .catch(err => {
          console.error(`Failed to save ${key}:`, err);
        });
    }
  }, [data, key, initialized]);
};
