import { useRef, useEffect, useState } from 'react';

export function useMinHeight(minHeight = 1) {
  const ref = useRef(null);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (!ref.current) return;

    const element = ref.current;
    const observer = new ResizeObserver(entries => {
      for (const entry of entries) {
        setIsVisible(entry.contentRect.height >= minHeight);
      }
    });

    observer.observe(element);
    return () => observer.disconnect();
  }, [minHeight]);

  return { ref, isVisible };
}
