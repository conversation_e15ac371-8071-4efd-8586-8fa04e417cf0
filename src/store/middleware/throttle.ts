import { Middleware } from '@reduxjs/toolkit';
import { isEqual, throttle } from 'lodash';
import { dragActions } from '../slices/drag';

export function throttleMiddleware(): Middleware {
  const configs = [{ type: dragActions.setDragging.type, wait: 10 }];
  const lastPayloadMap: Record<string, any> = {};
  const throttledMap: Record<string, any> = {};
  configs.forEach(({ type, wait = 100 }) => {
    throttledMap[type] = throttle((next, action) => {
      next(action);
    }, wait);
  });

  return () => next => (action: any) => {
    const { type, payload } = action;
    if (throttledMap[type]) {
      if (!isEqual(lastPayloadMap[type], payload)) {
        lastPayloadMap[type] = payload;
        throttledMap[type](next, action);
      }
      return;
    }
    return next(action);
  };
}
