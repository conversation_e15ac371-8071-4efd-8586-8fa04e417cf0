import { createSlice } from '@reduxjs/toolkit';

interface FlightData {
  flightMode: boolean;
  status: 'idle' | 'offline' | 'online';
  step: 0 | 1 | 2 | 3; // 0: initial, 1: selected, 2: download, 3: completed
  selectedModel?: { renaming?: string; foldering?: string };
}

const FlightSlice = createSlice({
  name: 'flight',
  initialState: {
    flightData: {
      flightMode: false,
      status: 'idle',
      step: 0, // Initial step
      selectedModel: undefined,
    } as FlightData,
  },
  reducers: {
    updateFlightData: (state, action) => {
      state.flightData = {
        ...state.flightData,
        ...action.payload,
      };
    },
    resetFlightData: state => {
      state.flightData = {
        step: 0,
        flightMode: false,
        status: 'idle',
      };
    },
  },
});

export const { actions: flightActions, reducer: flightReducer } = FlightSlice;
export default flightReducer;
