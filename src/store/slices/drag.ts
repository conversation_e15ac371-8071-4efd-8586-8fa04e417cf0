import { createSlice } from '@reduxjs/toolkit';

const DragSlice = createSlice({
  name: 'drag',
  initialState: {
    isDragging: false,
  },
  reducers: {
    setDragging: (state, action) => {
      state.isDragging = action.payload;
    },
    resetDragging: state => {
      state.isDragging = false;
    },
  },
});

export const { actions: dragActions, reducer: dragReducer } = DragSlice;
export default dragReducer;
