import { getModels, getModelsInstalled, getSelectedModel } from '@/api/api';
import { AiModelsBean, ModelType } from '@/api/models/AiModelsBean';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { cloneDeep } from 'lodash';

interface SettingsState {
  selectedModel?: ModelType;
  renameModels: AiModelsBean[];
  classifyModels: AiModelsBean[];
  renameDownloading: Record<string, any>;
  classifyDownloading: Record<string, any>;
  installedModels: AiModelsBean[];
}
const initialState: SettingsState = {
  selectedModel: undefined,
  renameModels: [],
  classifyModels: [],
  renameDownloading: {},
  classifyDownloading: [],
  installedModels: [],
};

export const fetchModels = createAsyncThunk('settings/fetchModels', async () => {
  const modelsRes = await getModels();
  const installedRes = await getModelsInstalled();
  for (const element of modelsRes.data) {
    if (element.name) {
      if (element.name.indexOf('Q8_0') > -1) {
        element.labelName = 'nonDestructiveModel';
      } else if (element.name.indexOf('Q5_K_M') > -1) {
        element.labelName = 'lowLossModel';
      } else if (element.name.indexOf('Q4_0') > -1) {
        element.labelName = 'highLossModel';
      } else {
        element.labelName = '';
      }
      // 如果已下载 则标记为已下载
      if (installedRes.data.find(e => e.name.includes(element.name))) {
        element.status = 'installed';
      } else {
        element.status = '';
      }
    }
  }
  return { data: modelsRes.data, installed: installedRes.data };
});

export const fetchSelectedModel = createAsyncThunk(
  'settings/fetchSelectedModel',
  async (_, thunkAPI) => {
    const state = thunkAPI.getState() as { settings: SettingsState };
    const model = await getSelectedModel();
    let selectedModel = state.settings.selectedModel;
    const rename =
      !model?.rename || model?.rename.type == 'Online' ? { type: 'Online' } : model.rename;
    const embedding =
      !model?.embedding || model?.embedding.type == 'Online' ? { type: 'Online' } : model.embedding;

    selectedModel = {
      ...selectedModel,
      rename,
      embedding,
    };
    return { selectedModel };
  }
);

export const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setSelectedModel: (state, action) => {
      state.selectedModel = action.payload;
    },
    addInstall: (
      state,
      action: {
        payload: {
          type: 'rename' | 'classify';
          data: any;
        };
        type: string;
      }
    ) => {
      if (action.payload.type === 'rename') {
        state.renameModels = state.renameModels.map(item => {
          return item.full_name === action.payload.data ? { ...item, status: 'installed' } : item;
        });
      }
      if (action.payload.type === 'classify') {
        state.classifyModels = state.classifyModels.map(item => {
          return item.full_name === action.payload.data ? { ...item, status: 'installed' } : item;
        });
      }
    },
    delInstall: (
      state,
      action: {
        payload: {
          type: 'rename' | 'classify';
          data: any;
        };
        type: string;
      }
    ) => {
      if (action.payload.type === 'rename') {
        state.renameModels = state.renameModels.map(item =>
          item.full_name === action.payload.data ? { ...item, status: '' } : item
        );
      }
      if (action.payload.type === 'classify') {
        state.classifyModels = state.classifyModels.map(item =>
          item.full_name === action.payload.data ? { ...item, status: '' } : item
        );
      }
    },
    addDownloading: (
      state,
      action: {
        payload: {
          type: 'rename' | 'classify';
          data: any;
        };
        type: string;
      }
    ) => {
      if (action.payload.type === 'rename') {
        state.renameDownloading = {
          ...state.renameDownloading,
          ...action.payload.data,
        };
      }

      if (action.payload.type === 'classify') {
        state.classifyDownloading = {
          ...state.classifyDownloading,
          ...action.payload.data,
        };
      }
    },
    delDownloading: (
      state,
      action: {
        payload: {
          type: 'rename' | 'classify';
          data: any;
        };
        type: string;
      }
    ) => {
      if (action.payload.type === 'rename') {
        const rl = cloneDeep(state.renameDownloading);
        delete rl[action.payload.data];
        state.renameDownloading = rl;
      }

      if (action.payload.type === 'classify') {
        const cl = cloneDeep(state.classifyDownloading);
        delete cl[action.payload.data];
        state.classifyDownloading = cl;
      }
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchModels.fulfilled, (state, action) => {
      const models = action.payload;
      state.renameModels = models.data
        .filter(model => model.type === 'Rename' && !!model.labelName)
        .sort((a, b) => {
          const order = {
            highLossModel: 2,
            nonDestructiveModel: 0,
            lowLossModel: 1,
          };
          return (
            (order[a.labelName as keyof typeof order] || 0) -
            (order[b.labelName as keyof typeof order] || 0)
          );
        });
      state.classifyModels = models.data
        .filter(model => model.type === 'Embedding' && !!model.labelName)
        .sort((a, b) => {
          const order = {
            highLossModel: 2,
            nonDestructiveModel: 0,
            lowLossModel: 1,
          };
          return (
            (order[a.labelName as keyof typeof order] || 0) -
            (order[b.labelName as keyof typeof order] || 0)
          );
        });
      state.installedModels = models.installed;
    });
    builder.addCase(fetchSelectedModel.fulfilled, (state, action) => {
      const models = action.payload;
      state.selectedModel = models.selectedModel;
    });
  },
});

export const { actions: settingsActions } = settingsSlice;

export default settingsSlice.reducer;
