import { createSlice } from '@reduxjs/toolkit';

interface DirState {
  directoryPath: string;
  show: boolean;
  embeddingData: {
    targetDir: string;
    status: 'idle' | 'processing' | 'success';
    num: number;
    total: number;
    progress: number;
  };
}

const initialState: DirState = {
  directoryPath: '',
  show: false,
  embeddingData: {
    targetDir: '',
    status: 'idle',
    num: 0,
    total: 0,
    progress: 0,
  },
};

const dirSlice = createSlice({
  name: 'dir',
  initialState,
  reducers: {
    updateDirectoryPath: (state, action) => {
      state.directoryPath = action.payload;
    },
    updateEmbeddingData: (state, action) => {
      state.embeddingData = {
        ...state.embeddingData,
        ...action.payload,
      };
    },
    resetEmbeddingData: state => {
      state.embeddingData = {
        targetDir: '',
        status: 'idle',
        num: 0,
        total: 0,
        progress: 0,
      };
    },
    toggleEmbedding: (state, action) => {
      state.show = action.payload;
    },
  },
});

export const { actions: dirActions, reducer: dirReducer } = dirSlice;
export default dirReducer;
