import { create } from 'zustand';
import { FileItem, TaskState } from '@/types/file';

const DEFAULT_TASK_STATE: TaskState = {
  files: [],
  pendingFiles: [],
  currentTaskMode: '',
  applied: false,
  renameFilesSameTime: false,
  classifyFilesSameTime: false,
  directoryPath: '',
  editingRowPath: '',
  formatList: [],
};

interface TasksStore {
  tasks?: Record<string, TaskState>;
  initialized: boolean;
  getTaskState: (tabKey: string) => TaskState;
  setTaskState: (tabKey: string, state: Partial<TaskState>) => void;
  removeTaskState: (tabKey: string) => void;
  updateFile: (tabKey: string, filePath: string, updates: Partial<FileItem>) => void;
  updateFiles: (tabKey: string, updates: Record<string, Partial<FileItem>>) => void;
  updatePendingFile: (tabKey: string, filePath: string, updates: Partial<FileItem>) => void;
  addPendingFiles: (tabKey: string, files: FileItem[]) => void;
  clearAppliedPendingFiles: (tabKey: string) => void;
  resetTaskState: (tabKey: string) => void;
}

export const useTasksStore = create<TasksStore>((set, get) => ({
  tasks: undefined,
  initialized: false,
  getTaskState: tabKey => {
    if (!tabKey) {
      throw new Error('Tab key is required to get task state');
    }
    const tasks = get().tasks || {};
    return tasks[tabKey] || { ...DEFAULT_TASK_STATE };
  },

  setTaskState: (tabKey, newState) => {
    set(state => ({
      tasks: {
        ...state.tasks,
        [tabKey]: {
          ...(state.tasks?.[tabKey] || DEFAULT_TASK_STATE),
          ...newState,
        },
      },
    }));
  },

  removeTaskState: tabKey => {
    set(state => {
      const newTasks = { ...state.tasks };
      delete newTasks[tabKey];
      return { tasks: newTasks };
    });
  },

  updateFile: (tabKey, filePath, updates) => {
    set(state => {
      const current = state.tasks?.[tabKey] || DEFAULT_TASK_STATE;
      return {
        tasks: {
          ...state.tasks,
          [tabKey]: {
            ...current,
            files: current.files.map(file =>
              file.path === filePath ? { ...file, ...updates } : file
            ),
          },
        },
      };
    });
  },

  updatePendingFile: (tabKey, filePath, updates) => {
    set(state => {
      const current = state.tasks?.[tabKey] || DEFAULT_TASK_STATE;
      return {
        tasks: {
          ...state.tasks,
          [tabKey]: {
            ...current,
            pendingFiles: current.pendingFiles.map(file =>
              file.path === filePath ? { ...file, ...updates } : file
            ),
          },
        },
      };
    });
  },

  updateFiles: (tabKey, updates) => {
    set(state => {
      const current = state.tasks?.[tabKey] || DEFAULT_TASK_STATE;
      return {
        tasks: {
          ...state.tasks,
          [tabKey]: {
            ...current,
            files: current.files.map(file =>
              updates[file.path] ? { ...file, ...updates[file.path] } : file
            ),
          },
        },
      };
    });
  },

  addPendingFiles: (tabKey, files) => {
    set(state => {
      const current = state.tasks?.[tabKey] || DEFAULT_TASK_STATE;
      const pendingFiles = current.pendingFiles || [];

      const fileMap = new Map<string, FileItem>();
      for (const file of pendingFiles) {
        fileMap.set(file.path, file);
      }
      for (const file of files) {
        fileMap.set(file.path, { ...fileMap.get(file.path), ...file });
      }
      const newFiles = Array.from(fileMap.values());

      return {
        tasks: {
          ...state.tasks,
          [tabKey]: {
            ...current,
            pendingFiles: newFiles,
          },
        },
      };
    });
  },

  clearAppliedPendingFiles: tabKey => {
    set(state => {
      const current = state.tasks?.[tabKey];
      if (!current) return state;

      return {
        tasks: {
          ...state.tasks,
          [tabKey]: {
            ...current,
            pendingFiles: [],
            applied: true,
          },
        },
      };
    });
  },

  resetTaskState: tabKey => {
    set(state => ({
      tasks: {
        ...state.tasks,
        [tabKey]: { ...DEFAULT_TASK_STATE },
      },
    }));
  },
}));
