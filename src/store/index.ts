import { configureStore } from '@reduxjs/toolkit';

import { throttleMiddleware } from './middleware/throttle';
import settingsReducer from './slices/settings';
import dragReducer from './slices/drag';
import flightReducer from './slices/flight';
import fileReducer from './slices/dir';

export const store = configureStore({
  reducer: {
    settings: settingsReducer,
    drag: dragReducer,
    flight: flightReducer,
    dir: fileReducer,
  },
  middleware: getDefaultMiddleware => getDefaultMiddleware().concat(throttleMiddleware()),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
