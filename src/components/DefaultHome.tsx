import { useTranslation } from 'react-i18next';

export default function DefaultHome() {
  const { t } = useTranslation();
  return (
    <div className="w-full h-[90vh] flex items-center justify-center flex-col bg-white rounded-[20px]">
      <div className="text-[50px] font-bold">{t('smartRenaming')}</div>
      <div className="text-[20px] font-bold mt-20 text-cyan-500">{t('clickSidebar')}</div>
      <div className="text-[20px] font-bold mt-4">{t('startWisfile')}</div>
    </div>
  );
}
