export default function DirectorySelect({
  paths,
  onSelect,
}: {
  paths: string[];
  onSelect: (path: string) => void;
}) {
  return (
    <div className="h-[200px] w-full overflow-y-auto border border-gray-200 rounded-md cursor-pointer">
      {paths.map((path, index) => (
        <div
          key={path}
          className={`px-4 py-2 border-b border-gray-200 text-center text-sm text-gray-500 hover:bg-gray-100  cursor-pointer ${
            index === paths.length - 1 ? 'border-b-0' : ''
          }`}
          onClick={() => onSelect(path)}
        >
          {path}
        </div>
      ))}
    </div>
  );
}
