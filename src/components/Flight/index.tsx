import { RootState } from '@/store';
import Download from './Download';
import SelectModels from './SelectModels';
import Welcome from './Welcome';
import { useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
const Flight = (): JSX.Element => {
  const flightData = useSelector((state: RootState) => state.flight.flightData);
  const { step = 0 } = flightData || {};

  const getAnimationConfig = () => {
    return {
      variants: {
        enter: {
          opacity: 0,
        },
        center: {
          opacity: 1,
        },
        exit: {
          opacity: 0,
        },
      },
      transition: {
        type: 'tween' as const,
        ease: 'easeInOut' as const,
        duration: 0.3,
      },
    };
  };

  const animationConfig = getAnimationConfig();

  const renderStep = () => {
    switch (step) {
      case 0:
        return <Welcome />;
      case 1:
        return <SelectModels />;
      case 2:
        return <Download />;
      default:
        return <Welcome />;
    }
  };

  return (
    <div className="top-0 left-0 absolute flex flex-col w-full h-full overflow-hidden">
      <div
        data-tauri-drag-region
        className="top-0 left-0 z-100 absolute w-full h-11 cursor-auto"
      ></div>

      <div className="relative w-full h-full">
        <AnimatePresence mode="wait">
          <motion.div
            key={step}
            variants={animationConfig.variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={animationConfig.transition}
            className="absolute inset-0 w-full h-full"
          >
            {renderStep()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Flight;
