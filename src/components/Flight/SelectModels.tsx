import { uiEvent } from '@/api/api';
import { RootState } from '@/store';
import { flightActions } from '@/store/slices/flight';
import { formatBytesUp } from '@/utils/byte';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

const SelectModels = () => {
  const { t } = useTranslation();
  const [selected, setSelected] = useState<{ renaming?: string; foldering?: string }>();

  const dispatchRTK = useDispatch();
  const settings = useSelector((state: RootState) => state.settings);
  const { renameModels, classifyModels } = settings;

  const newModelOptions = useMemo(() => {
    const modelOptions = [
      {
        group: 'renameFiles',
        options: renameModels
          .filter(item => item.labelName != 'nonDestructiveModel')
          .map(model => ({
            name: model.full_name,
            sha256: model.sha256,
            size: model.size,
            type: model.type,
            labelName: model.labelName,
            desc: model.labelName === 'lowLossModel' ? 'standardDesc' : 'miniDesc',
          })),
      },
      {
        group: 'filesClassification',
        options: classifyModels
          .filter(item => item.labelName != 'nonDestructiveModel')
          .map(model => ({
            name: model.full_name,
            sha256: model.sha256,
            size: model.size,
            type: model.type,
            labelName: model.labelName,
            desc: model.labelName === 'lowLossModel' ? 'standardDesc' : 'miniDesc',
          })),
      },
    ];
    return modelOptions;
  }, [renameModels, classifyModels]);

  useEffect(() => {
    if (renameModels && classifyModels) {
      const renamingModel = renameModels.find(model => model.labelName === 'lowLossModel');
      const folderingModel = classifyModels.find(model => model.labelName === 'lowLossModel');

      setSelected({
        renaming: renamingModel ? renamingModel.full_name : '',
        foldering: folderingModel ? folderingModel.full_name : '',
      });
    }
  }, [renameModels, classifyModels]);

  useEffect(() => {
    if (selected) {
      dispatchRTK(
        flightActions.updateFlightData({
          selectedModel: selected,
        })
      );
    }
  }, [selected, dispatchRTK]);

  if (!selected) {
    return null;
  }

  return (
    <div className="flex flex-col justify-center items-center w-full h-full">
      <div className="flex flex-col items-start mx-auto w-full max-w-[600px]">
        <div className="flex font-bold text-xl text-center">{t('chooseModels')}</div>
        <div className="flex flex-col gap-y-4 mt-8 w-full">
          {newModelOptions.map(group => (
            <div key={group.group} className="bg-[#FAFAFA] px-3 py-2 border rounded-xl w-full">
              <div className="mb-2 font-bold text-[#333] text-sm">{t(group.group)}</div>
              <div className="space-y-2">
                {group.options.map(opt => {
                  const isRenaming = group.group === 'renameFiles';
                  const groupKey = isRenaming ? 'renaming' : 'foldering';
                  const checked = selected[groupKey] === opt.name;
                  return (
                    <label
                      key={opt.name}
                      className={`flex items-center justify-between p-2 rounded-xl border cursor-pointer transition-all ${
                        checked ? 'border-[#FFDF75] bg-[#FFF9E5]' : 'border-[#EBEBEB] bg-white'
                      }`}
                    >
                      <div className="flex items-center">
                        <span
                          className={`w-[17px] h-[17px] mr-4 flex items-center justify-center rounded-full border ${
                            checked ? 'border-[#FFDF75]' : 'border-[#EBEBEB] bg-white'
                          }`}
                        >
                          {checked && (
                            <span className="block bg-[#FFDF75] rounded-full w-[13px] h-[13px]" />
                          )}
                        </span>
                        <div>
                          <div className="text-[#333] text-sm">{t(opt.labelName)}</div>
                          <div className="text-[#999] text-xs">{t(opt.desc)}</div>
                        </div>
                      </div>
                      <div className="font-medium text-[#333] text-[10px]">
                        {formatBytesUp(opt.size)}
                      </div>
                      <input
                        type="radio"
                        name={groupKey}
                        className="hidden"
                        checked={checked}
                        onChange={() => setSelected(prev => ({ ...prev, [groupKey]: opt.name }))}
                      />
                    </label>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
        <div className="flex justify-end gap-4 mt-12 w-full">
          <div
            className="flex justify-center items-end bg-[#FFDF75] py-2 border border-[#FFDF75] rounded-lg w-[136px] text-[#333] text-xs hover:scale-105 transition cursor-pointer"
            onClick={() => {
              dispatchRTK(
                flightActions.updateFlightData({
                  step: 2,
                })
              );
            }}
          >
            {t('nextStep')}
          </div>
          <div
            className="flex justify-center items-end py-2 border border-[#EBEBEB] rounded-lg w-[136px] text-[#333] text-xs hover:scale-105 transition cursor-pointer"
            onClick={() => {
              uiEvent('Click', { Back: 'rechoose' });

              dispatchRTK(
                flightActions.updateFlightData({
                  step: 0,
                  status: 'idle',
                })
              );
            }}
          >
            {t('back')}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectModels;
