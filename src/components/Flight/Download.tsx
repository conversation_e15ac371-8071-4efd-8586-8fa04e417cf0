import { downloadModel, getDownloadProgress, switchModel } from '@/api/api';
import { AiModelsBean } from '@/api/models/AiModelsBean';
import { AppDispatch, RootState } from '@/store';
import { flightActions } from '@/store/slices/flight';
import { fetchSelectedModel, settingsActions } from '@/store/slices/settings';
import { info } from '@tauri-apps/plugin-log';
import { Image, message, Progress } from 'antd';
import { cloneDeep } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { Swiper, SwiperSlide } from 'swiper/react';
import { EffectCreative, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/effect-creative';
import classNames from 'classnames';
import flight01 from '@/assets/png/flight-01.png';
import flight02 from '@/assets/png/flight-02.png';
import flight03 from '@/assets/png/flight-03.png';
import flight04 from '@/assets/png/flight-04.png';

const Download = (): JSX.Element => {
  const { t } = useTranslation();
  const flightData = useSelector((state: RootState) => state.flight.flightData);

  const selectedModelRef = useRef<typeof selectedModel | null | undefined>(null);

  const dispatchRTK = useDispatch<AppDispatch>();
  const settings = useSelector((state: RootState) => state.settings);
  const { selectedModel, renameModels, classifyModels } = settings;

  // 本地状态追踪下载进度
  const [downloadProgress, setDownloadProgress] = useState({
    total: 0,
    isDownloading: false,
    renameProgress: 0,
    classifyProgress: 0,
  });

  // 追踪轮询状态以便取消
  const pollingRef = useRef<{ polling: boolean }>({ polling: false });
  useEffect(() => {
    selectedModelRef.current = selectedModel;
  }, [selectedModel]);

  useEffect(() => {
    if ((renameModels.length > 0, classifyModels.length > 0)) {
      dispatchRTK(fetchSelectedModel());
    }
  }, [renameModels, classifyModels, dispatchRTK]);

  const handleModelCompletion = useCallback(
    async (mod: 'rename' | 'classify', model: AiModelsBean) => {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const req = cloneDeep(selectedModelRef.current);
      if (!req) return;
      if (mod === 'rename') {
        req.rename = {
          type: 'Local',
          data: {
            name: model.full_name,
            sha256: model.sha256,
            size: model.size,
            type: model.type,
          },
        };
      } else {
        req.embedding = {
          type: 'Local',
          data: {
            name: model.full_name,
            sha256: model.sha256,
            size: model.size,
            type: model.type,
          },
        };
      }

      await switchModel(req);
      dispatchRTK(settingsActions.setSelectedModel(req));
      dispatchRTK(
        settingsActions.addInstall({
          type: mod,
          data: model.full_name,
        })
      );
    },
    [dispatchRTK]
  );

  const downloadBothModels = useCallback(
    async (renameModel: AiModelsBean, classifyModel: AiModelsBean) => {
      try {
        // 设置开始下载状态
        setDownloadProgress({
          total: 0,
          isDownloading: true,
          renameProgress: 0,
          classifyProgress: 0,
        });

        // 同时启动两个模型的下载
        const [renameResult, classifyResult] = await Promise.all([
          downloadModel([{ name: renameModel.name, type: renameModel.type }]),
          downloadModel([{ name: classifyModel.name, type: classifyModel.type }]),
        ]);

        // 检查下载结果
        if (!renameResult.success || renameResult.data.length === 0) {
          message.error(t('downloadFailed') + ' (Rename Model)');
          info(`重命名模型下载失败`);
          setDownloadProgress(prev => ({ ...prev, isDownloading: false }));
          return;
        }

        if (!classifyResult.success || classifyResult.data.length === 0) {
          message.error(t('downloadFailed') + ' (Classify Model)');
          info(`分类模型下载失败`);
          setDownloadProgress(prev => ({ ...prev, isDownloading: false }));
          return;
        }

        const renameUuid = renameResult.data[0].uuid;
        const classifyUuid = classifyResult.data[0].uuid;

        info(`开始轮询两个模型的下载进度`);

        // 追踪下载状态
        const downloadStatus = {
          rename: { completed: false, progress: 0 },
          classify: { completed: false, progress: 0 },
        };

        let polling = true;
        pollingRef.current.polling = true;
        let hasNotified = false; // 防止重复通知

        const pollBothDownloads = async () => {
          if (!polling || !pollingRef.current.polling) return;

          try {
            // 同时获取两个模型的下载进度
            const [renameProgress, classifyProgress] = await Promise.all([
              getDownloadProgress([renameUuid]),
              getDownloadProgress([classifyUuid]),
            ]);

            // 处理重命名模型进度
            if (renameProgress.success && renameProgress.data.length > 0) {
              const renameData = renameProgress.data[0];
              if (renameData.status.type === 'Done' && !downloadStatus.rename.completed) {
                downloadStatus.rename.completed = true;
                downloadStatus.rename.progress = 100;
                info(`重命名模型下载完成`);
                await handleModelCompletion('rename', renameModel);
              } else if (renameData.status.type !== 'Done') {
                downloadStatus.rename.progress = Math.floor(
                  (renameData.status.content / renameData.total) * 100
                );
              }
            }

            // 处理分类模型进度
            if (classifyProgress.success && classifyProgress.data.length > 0) {
              const classifyData = classifyProgress.data[0];
              if (classifyData.status.type === 'Done' && !downloadStatus.classify.completed) {
                downloadStatus.classify.completed = true;
                downloadStatus.classify.progress = 100;
                info(`分类模型下载完成`);
                await handleModelCompletion('classify', classifyModel);
              } else if (classifyData.status.type !== 'Done') {
                downloadStatus.classify.progress = Math.floor(
                  (classifyData.status.content / classifyData.total) * 100
                );
              }
            }

            // 计算总进度 (两个模型的平均进度)
            const totalProgress = Math.floor(
              (downloadStatus.rename.progress + downloadStatus.classify.progress) / 2
            );

            // 更新本地状态
            setDownloadProgress({
              total: totalProgress,
              isDownloading: true,
              renameProgress: downloadStatus.rename.progress,
              classifyProgress: downloadStatus.classify.progress,
            });

            info(
              `总进度: ${totalProgress}% (重命名: ${downloadStatus.rename.progress}%, 分类: ${downloadStatus.classify.progress}%)`
            );

            // 检查是否两个模型都下载完成
            if (
              downloadStatus.rename.completed &&
              downloadStatus.classify.completed &&
              !hasNotified
            ) {
              polling = false;
              pollingRef.current.polling = false;
              hasNotified = true; // 标记已通知，防止重复
              info(`所有模型下载完成`);
              setDownloadProgress(prev => ({ ...prev, isDownloading: false, total: 100 }));
              return;
            }

            // 继续轮询
            if (polling && pollingRef.current.polling) {
              setTimeout(pollBothDownloads, 1000);
            }
          } catch (error) {
            info(`轮询下载进度时出错: ${error}`);
            setTimeout(pollBothDownloads, 1000);
          }
        };

        // 开始轮询
        pollBothDownloads();
      } catch (error) {
        message.error(t('downloadFailed'));
        info(`下载模型时出错: ${error}`);
        setDownloadProgress(prev => ({ ...prev, isDownloading: false }));
      }
    },
    [t, handleModelCompletion, setDownloadProgress]
  );

  const onStart = () => {
    dispatchRTK(
      flightActions.updateFlightData({
        step: 3,
        status: 'offline',
      })
    );
  };

  useEffect(() => {
    if (flightData?.selectedModel && renameModels.length > 0 && classifyModels.length > 0) {
      const rm = renameModels.find(item => item.full_name === flightData.selectedModel?.renaming);
      const cm = classifyModels.find(
        item => item.full_name === flightData.selectedModel?.foldering
      );

      const isInstalled =
        renameModels.some(item => item.status === 'installed') ||
        classifyModels.some(item => item.status === 'installed');

      if (rm && cm && !isInstalled) {
        downloadBothModels(rm, cm);
      } else {
        setDownloadProgress({
          total: 100,
          isDownloading: false,
          renameProgress: 100,
          classifyProgress: 100,
        });
      }
    }
  }, [flightData, renameModels, classifyModels, downloadBothModels]);

  return (
    <div className="flex flex-col justify-center items-center mx-auto w-full max-w-[667px] h-full overflow-y-auto">
      <div className="w-full h-[372px] aspect-[621/372]">
        <Swiper
          grabCursor={true}
          loop={true}
          effect={'creative'}
          creativeEffect={{
            prev: {
              shadow: true,
              translate: [0, 0, -400],
            },
            next: {
              translate: ['100%', 0, 0],
            },
          }}
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
            reverseDirection: true,
          }}
          modules={[EffectCreative, Autoplay]}
          className="w-full h-full"
        >
          <SwiperSlide>
            <div className="flex justify-center items-center bg-[#FFDF75] rounded-xl w-full h-full overflow-hidden">
              <img className="object-contain" src={flight01} alt="" />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className="flex justify-center items-center rounded-xl w-full h-full overflow-hidden">
              <Image className="object-cover" src={flight02} preview={false} />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className="flex justify-center items-center rounded-xl w-full h-full overflow-hidden">
              <Image className="object-fill" src={flight03} preview={false} />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className="flex justify-center items-center rounded-xl w-full h-full overflow-hidden">
              <Image className="object-fill" src={flight04} preview={false} />
            </div>
          </SwiperSlide>
        </Swiper>
      </div>
      <div className="mt-[60px] font-bold text-base">
        {downloadProgress.total >= 100 ? t('modelsDownloadComplete') : t('weAcceleratingDownload')}
      </div>
      <div className="flex mt-6 px-[30px] w-full">
        <Progress percent={downloadProgress.total} showInfo={false} />
      </div>
      <div
        className={classNames(
          'justify-center items-end bg-[#FFDF75] mt-10 py-2 border border-[#FFDF75] rounded-lg w-[136px] text-[#333] text-xs transition cursor-pointer',
          {
            flex: downloadProgress.total >= 100,
            hidden: downloadProgress.total < 100,
          }
        )}
        onClick={onStart}
      >
        {t('start')}
      </div>
    </div>
  );
};

export default Download;
