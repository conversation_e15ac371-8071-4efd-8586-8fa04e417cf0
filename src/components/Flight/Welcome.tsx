import { uiEvent } from '@/api/api';
import LogoIcon from '@/assets/svg/logo.svg?react';
import QuestionMarkIcon from '@/assets/svg/question-mark.svg?react';
import { flightActions } from '@/store/slices/flight';
import { Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
const Welcome = () => {
  const { t } = useTranslation();
  const dispatchRTK = useDispatch();

  return (
    <div className="flex flex-col justify-center items-center mx-auto max-w-[516px] h-full -translate-y-10">
      <div className="flex items-center gap-x-9">
        <LogoIcon className="w-fit h-fit" />
        <div>
          <div className="font-bold text-[#333] text-[20px]">{t('welcometoWisfile')}</div>
          <div className="mt-1 text-[#333] text-xs">{t('welcometowisfiledescription')}</div>
        </div>
      </div>
      <div className="mt-10 font-bold text-[#333]">{t('chooseYourMode')}</div>
      <div className="flex gap-x-4 mt-8">
        <div
          className="flex justify-center items-center gap-x-1 bg-[#FFDF75] px-6 border rounded-lg h-[34px] text-[#333] text-xs hover:scale-105 transition cursor-pointer"
          onClick={() => {
            uiEvent('Click', { Mode: 'offline' });
            dispatchRTK(
              flightActions.updateFlightData({
                step: 1,
              })
            );
          }}
        >
          <span>{t('offlineMode')}</span>
          <Tooltip title={t('onlineTips')}>
            <QuestionMarkIcon className="text-[#333]" />
          </Tooltip>
        </div>
        <div
          className="flex justify-center items-center gap-x-1 px-6 border border-[#EBEBEB] rounded-lg h-[34px] text-[#333] text-xs hover:scale-105 transition cursor-pointer"
          onClick={() => {
            uiEvent('Click', { Mode: 'online' });
            dispatchRTK(
              flightActions.updateFlightData({
                step: 3,
                status: 'online',
              })
            );
          }}
        >
          {t('onlineMode')}
        </div>
      </div>
    </div>
  );
};

export default Welcome;
