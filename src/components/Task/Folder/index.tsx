import { getFileName } from '@/utils/string';
import FolderIcon from '@/assets/svg/folder.svg?react';
import { getSubDirs } from './var';
import { Modal, Skeleton } from 'antd';
import { useTranslation } from 'react-i18next';
import DirectorySelect from '@/components/DirectorySelect';
import { info } from '@tauri-apps/plugin-log';
import { useTasksStore } from '@/store/tasksStore';
import { useSearchParams } from 'react-router-dom';
import { FileItem } from '@/types/file';
import { uiEvent } from '@/api/api';
interface FolderProps {
  record: any; // 文件夹记录对象
}

const Folder: React.FC<FolderProps> = ({ record }) => {
  const { cStatus } = record;
  const [searchParams] = useSearchParams();
  const tabKey = searchParams.get('tabKey') || '';
  const { t } = useTranslation();
  const { getTaskState, setTaskState } = useTasksStore();
  const { directoryPath, files, pendingFiles } = getTaskState(tabKey);

  const onOperation = (path: string, file: FileItem) => {
    const newFiles = files.map(f => {
      if (f.path === path) {
        return { ...f, ...file };
      }
      return f;
    });

    info(
      `[updateFile] 更新文件: ${path},\n 旧数据: ${JSON.stringify(
        files
      )}\n 新数据: ${JSON.stringify(newFiles)}\n 变更: ${JSON.stringify(file)}`
    );

    const newPendingFiles = pendingFiles.map(f => {
      if (f.path === path) {
        return { ...f, ...file };
      }
      return f;
    });

    info(
      `[updatePendingFile] 更新待处理文件: ${path},\n 旧数据: ${JSON.stringify(
        pendingFiles
      )}\n 新数据: ${JSON.stringify(newPendingFiles)} \n 变更: ${JSON.stringify(file)}`
    );

    setTaskState(tabKey, {
      files: newFiles,
      pendingFiles: newPendingFiles,
    });
  };

  return (
    <>
      {cStatus === 'Processing' || cStatus === 'Waiting' ? (
        <Skeleton active title={false} paragraph={{ rows: 1 }} />
      ) : (
        <div className="group flex justify-between items-center">
          <span className="text-[#999] text-[12px] truncate pr-2 min-w-0 flex-1">
            {getFileName(record.classifying)}
          </span>
          <FolderIcon
            className="opacity-0 group-hover:opacity-100 cursor-pointer flex-shrink-0 w-[18px] h-[18px]"
            onClick={async () => {
              setTaskState(tabKey, { applied: true });
              uiEvent('Move', { Filehanding: 'move' });
              // 获取当前文件夹的子目录
              const subDirs = await getSubDirs(directoryPath, record.classifying || '');
              Modal.confirm({
                title: t('selectDirectory'),
                closable: true,
                icon: null,
                width: 400,
                height: 200,
                footer: null,
                onCancel: () => {},
                content: (
                  <DirectorySelect
                    paths={subDirs}
                    onSelect={path => {
                      Modal.destroyAll();
                      const newPath = directoryPath + '/' + path;
                      info(`[选择目录] 选择的目录: ${newPath}`);
                      info(`[选择目录] 当前文件: ${record.path}`);
                      info(`[选择目录] 当前文件名: ${JSON.stringify(record)}`);

                      console.log('选择的目录:', newPath);

                      onOperation(record.path, {
                        ...record,
                        classifying: newPath,
                        cStatus: 'ToApply',
                      });
                      setTaskState(tabKey, { applied: false });
                    }}
                  />
                ),
              });
            }}
          />
        </div>
      )}
    </>
  );
};

export default Folder;
