import TruncatedText from '@/components/TruncatedText';
import { useTasksStore } from '@/store/tasksStore';
import { FileItem } from '@/types/file';
import { info } from '@tauri-apps/plugin-log';
import { Input, InputRef } from 'antd';
import { useEffect, useRef, useState } from 'react';
import EditIcon from '@/assets/svg/edit.svg?react';
import classNames from 'classnames';
import { uiEvent } from '@/api/api';
interface EditableCellProps {
  tabKey: string; // 当前标签页key
  recordKey: string; // 当前行唯一标识（如文件路径）
  fieldName: string; // 要编辑的字段名（如"fileName"）
  value: string;
  record: FileItem; // 可选的文件记录对象
  [key: string]: any;
}

const EditableCell: React.FC<EditableCellProps> = ({
  tabKey,
  recordKey,
  fieldName,
  record,
  value: initialValue,
  ...restProps
}) => {
  const inputRef = useRef<InputRef>(null);
  const [editing, setEditing] = useState(false);
  const [currentValue, setCurrentValue] = useState(initialValue);
  const { getTaskState, setTaskState } = useTasksStore();
  const { files } = getTaskState(tabKey);

  // 同步外部值变化
  useEffect(() => {
    setCurrentValue(initialValue);
  }, [initialValue]);

  // 进入编辑模式自动聚焦
  useEffect(() => {
    if (editing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [editing]);

  const handleSave = (path: string, file: FileItem) => {
    const updatedFiles = files.map(f => (f.path === path ? { ...f, ...file } : f));
    const pendingFiles = files.map(f => (f.path === recordKey ? { ...f, ...file } : f));
    uiEvent('Hand_rename', { Filehanding: 'hand_rename' });
    // 更新zustand状态
    setTaskState(tabKey, { files: updatedFiles, pendingFiles });
    setEditing(false);
  };

  return (
    <div className="group flex items-center gap-2 w-full overflow-hidden">
      {editing ? (
        <Input
          className={classNames('flex-1 h-fit box-border')}
          ref={inputRef}
          value={currentValue}
          onChange={e => {
            if ((e.nativeEvent as InputEvent).isComposing) return;
            setCurrentValue(e.target.value);
          }}
          onCompositionEnd={e => {
            setCurrentValue((e.target as HTMLInputElement).value);
          }}
          onPressEnter={() => {
            if (currentValue !== initialValue) {
              setTaskState(tabKey, {
                applied: false,
              });
              handleSave(recordKey, {
                ...record,
                [fieldName]: currentValue,
                rStatus: 'ToApply',
              });
            }
          }}
          onBlur={e => {
            if (e.target.value.trim() === '') {
              setCurrentValue(initialValue);
              setEditing(false);
            } else {
              if (e.target.value !== initialValue) {
                setTaskState(tabKey, {
                  applied: false,
                });
                info(`[EditableCell] 保存新值: ${e.target.value}`);
                handleSave(recordKey, {
                  ...record,
                  [fieldName]: e.target.value,
                  rStatus: 'ToApply',
                });
              } else {
                info(`[EditableCell] 没有提供 onSave 回调，直接更新状态`);
              }
            }
          }}
          size="small"
          variant="outlined"
          {...restProps}
        />
      ) : (
        <TruncatedText
          className="h-[18px] text-[12px]"
          onClick={() => {
            setEditing(true);
          }}
          title={currentValue || ''}
        >
          {currentValue}
        </TruncatedText>
      )}

      <EditIcon
        className="opacity-0 group-hover:opacity-100 cursor-pointer"
        onClick={() => setEditing(true)}
      />
    </div>
  );
};

export default EditableCell;
