import { items } from './var';
import classNames from 'classnames';
import { useSearchParams } from 'react-router-dom';
import { useTasksStore } from '@/store/tasksStore';
import { FileItem } from '@/types/file';
import { info } from '@tauri-apps/plugin-log';
import { RenameClassify } from '@/pages/tasks/type';
import {
  collectionUploadFile,
  createCollection,
  moveFile,
  renameAndClassify,
  renameFile,
  uiEvent,
} from '@/api/api';
import { Channel } from '@tauri-apps/api/core';
import { useTranslation } from 'react-i18next';
import { Input, Modal, notification, Tooltip } from 'antd';
import { cloneDeep } from 'lodash';
import React, { useState } from 'react';
import IssueIcon from '@/assets/svg/issue-warning.svg?react';
import IssueSuccessIcon from '@/assets/svg/issue-success.svg?react';
import { isValidEmail } from '@/utils/email';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
interface OperationProps {
  record: any;
}

export const Operation = React.memo((props: OperationProps) => {
  const [api, contextHolder] = notification.useNotification();
  const { t } = useTranslation();
  const { record } = props;
  const [searchParams] = useSearchParams();
  const tabKey = searchParams.get('tabKey') || '';
  const { getTaskState, setTaskState } = useTasksStore();
  const { updateFile, addPendingFiles, updatePendingFile } = useTasksStore.getState();
  const [open, setOpen] = useState(false);
  const [successOpen, setSuccessOpen] = useState(false);
  const flight = useSelector((state: RootState) => state.flight.flightData);
  const { status: flightStatus } = flight;

  const [email, setEmail] = useState<string | undefined>();
  const {
    directoryPath,
    files,
    pendingFiles,
    formatList,
    renameFilesSameTime,
    classifyFilesSameTime,
    currentTaskMode,
  } = getTaskState(tabKey);

  const openNotification = (pauseOnHover: boolean, message?: string, description?: string) => {
    api.open({
      message: message || '',
      description: description || '',
      pauseOnHover,
    });
  };

  const onOperation = (path: string, file: FileItem) => {
    const newFiles = files.map(f => {
      if (f.path === path) {
        return { ...f, ...file };
      }
      return f;
    });

    info(
      `[updateFile] 更新文件: ${path},\n 旧数据: ${JSON.stringify(
        files
      )}\n 新数据: ${JSON.stringify(newFiles)}\n 变更: ${JSON.stringify(file)}`
    );

    const newPendingFiles = pendingFiles.map(f => {
      if (f.path === path) {
        return { ...f, ...file };
      }
      return f;
    });

    info(
      `[updatePendingFile] 更新待处理文件: ${path},\n 旧数据: ${JSON.stringify(
        pendingFiles
      )}\n 新数据: ${JSON.stringify(newPendingFiles)} \n 变更: ${JSON.stringify(file)}`
    );

    setTaskState(tabKey, {
      files: newFiles,
      pendingFiles: newPendingFiles,
    });
  };

  const apply = async () => {
    const newPendingFiles = cloneDeep(pendingFiles).filter(f => f.name === record.name);

    info(`[手动应用] 待处理文件数: ${newPendingFiles.length}`);

    if (newPendingFiles.length === 0) {
      openNotification(false, t('noFilesToProcess'));
      return;
    }

    let successCount = 0;
    let failedCount = 0;
    const errorMessages: string[] = [];

    for (const pendingFile of newPendingFiles) {
      const theFile = files.find(f => f.path === pendingFile.path);
      info(`[处理文件] 正在处理文件tabKey: ${tabKey}`);
      info(`[处理文件] 正在处理文件files: ${JSON.stringify(files)}`);
      info(`[处理文件] 正在处理文件pendingFile: ${JSON.stringify(pendingFile)}`);
      info(`[处理文件] 正在处理文件theFile: ${JSON.stringify(theFile)}`);
      info(`[处理文件] ${pendingFile.path} 状态: ${theFile?.rStatus}, ${theFile?.cStatus}`);

      if (!theFile) {
        continue; // 如果文件不存在，跳过处理
      }

      if (currentTaskMode === 'rename' && classifyFilesSameTime) {
        // 两个同时判断
        if (theFile?.renaming && theFile?.classifying) {
          const source = pendingFile.path;
          const targetRename = theFile.renaming;
          const targetClassify = theFile.classifying;
          if (
            theFile.rStatus === 'ApplyFinished' &&
            source.includes(targetRename) &&
            theFile.cStatus === 'ApplyFinished' &&
            source.includes(targetClassify)
          ) {
            info(`[跳过] 文件 ${pendingFile.path} 重命名和分类目标与源相同，跳过处理`);
            continue;
          }
        }
      } else if (currentTaskMode === 'rename') {
        // 判断一类
        if (theFile?.renaming) {
          const source = pendingFile.path;
          const target = theFile.renaming;
          if (theFile.rStatus === 'ApplyFinished' && source.includes(target)) {
            info(`[跳过] 文件 ${pendingFile.path} 重命名目标与源相同，跳过处理`);
            continue;
          }
        }
      }
      if (currentTaskMode === 'classify' && renameFilesSameTime) {
        if (theFile?.renaming && theFile?.classifying) {
          const source = pendingFile.path;
          const targetRename = theFile.renaming;
          const targetClassify = theFile.classifying;
          if (
            theFile.rStatus === 'ApplyFinished' &&
            source.includes(targetRename) &&
            theFile.cStatus === 'ApplyFinished' &&
            source.includes(targetClassify)
          ) {
            info(`[跳过] 文件 ${pendingFile.path} 重命名和分类目标与源相同，跳过处理`);
            continue;
          }
        }
      } else if (currentTaskMode === 'classify') {
        // 判断一类
        if (theFile?.classifying) {
          const source = pendingFile.path;
          const target = theFile.classifying;
          console.log(`[处理文件] 分类目标: ${target}, 源路径: ${source}`);

          if (theFile.cStatus === 'ApplyFinished' && source.includes(target)) {
            info(`[跳过] 文件 ${pendingFile.path} 分类目标与源相同，跳过处理`);
            continue;
          }
        }
      }

      try {
        // 1. 标记为 Processing
        updateFile(tabKey, pendingFile.path, {
          rStatus: 'Processing',
          cStatus: 'Processing',
          progress: 0,
        });

        let newPath = pendingFile.path;
        const operationLog = [];
        const updatePayload: Partial<FileItem> = {};

        // 2. 处理重命名
        if (pendingFile.renaming) {
          try {
            const renameRes = await renameFile({
              source: newPath,
              target: pendingFile.renaming || '',
            });

            info(`[重命名结果] ${JSON.stringify(renameRes)}`);

            if (!renameRes.success) {
              throw new Error(`rename failed: ${renameRes.msg}`);
            }

            newPath = renameRes.msg || newPath;
            operationLog.push(`rename success: ${newPath}`);
            updatePayload.rStatus = 'ApplyFinished';
          } catch (error) {
            console.log(`[处理失败] 000文件 ${pendingFile.path} 处理失败: ${error}`);

            const errorMsg = error instanceof Error ? error.message : String(error);
            errorMessages.push(`file ${pendingFile.path} process failed: ${errorMsg}`);
            info(`[处理失败] 文件 ${pendingFile.path} 处理失败: ${errorMsg}`);
            updatePayload.rStatus = 'Failed';
            updatePayload.msg = errorMsg;
            // 跳过后续移动操作
            throw error;
          }
        }

        console.log('=====pendingFile=====', pendingFile);

        // 3. 处理移动
        if (pendingFile.classifying) {
          try {
            const moveRes = await moveFile({
              source: newPath,
              target: pendingFile.classifying || '',
            });

            info(`[移动结果] ${JSON.stringify(moveRes)}`);

            if (!moveRes.success) {
              throw new Error(`move failed: ${moveRes.msg}`);
            }

            newPath = moveRes.msg || newPath;
            operationLog.push(`move success: ${newPath}`);
            updatePayload.cStatus = 'ApplyFinished';
          } catch (error) {
            console.log(`[处理失败] 111文件 ${pendingFile.path} 处理失败: ${error}`);

            const errorMsg = error instanceof Error ? error.message : String(error);
            errorMessages.push(`file ${pendingFile.path} process failed: ${errorMsg}`);
            info(`[处理失败] 文件 ${pendingFile.path} 处理失败: ${errorMsg}`);
            updatePayload.cStatus = 'Failed';
            updatePayload.msg = (updatePayload.msg ? `${updatePayload.msg}; ` : '') + errorMsg;
            throw error;
          }
        }

        info(`[处理完成] 文件 ${pendingFile.path} 处理完成，新路径: ${newPath}`);

        // 4. 统一更新最终状态
        updateFile(tabKey, pendingFile.path, {
          ...updatePayload,
          path: newPath,
          progress: 100,
          msg: operationLog.join('; '),
        });

        updatePendingFile(tabKey, pendingFile.path, {
          ...pendingFile,
          ...updatePayload,
          path: newPath,
          progress: 100,
          msg: operationLog.join('; '),
        });

        successCount++;
      } catch (error) {
        // 捕获重命名或移动中的异常
        const errorMsg = error instanceof Error ? error.message : String(error);
        info(`[处理失败] 文件 ${pendingFile.path} 处理失败: ${errorMsg}`);
        info(`[处理失败] 错误详情: ${JSON.stringify(error)}`);
        info(`[处理失败] pendingFile: ${JSON.stringify(pendingFile)}`);

        // 更新失败状态
        const failedUpdate = files.find(f => f.path === pendingFile.path);
        updateFile(tabKey, pendingFile.path, {
          ...failedUpdate,
          msg: errorMsg,
        });

        updatePendingFile(tabKey, pendingFile.path, {
          ...pendingFile,
          ...failedUpdate,
          msg: errorMsg,
        });

        failedCount++;
        continue; // 跳过当前文件，继续处理下一个
      }
    }

    // 显示汇总通知
    if (failedCount === 0) {
      openNotification(true, t('applySuccessCount', { count: successCount }));
    } else {
      openNotification(
        false,
        t('applyPartialSuccess', { success: successCount, failed: failedCount }),
        errorMessages.join('\n')
      );
    }
  };

  const onReportIssue = async () => {
    setEmail('');
    setOpen(false);
    setSuccessOpen(true);

    let issueKind = '';
    if (
      (currentTaskMode === 'rename' && renameFilesSameTime) ||
      (currentTaskMode === 'classify' && classifyFilesSameTime)
    ) {
      issueKind = 'Both';
    }
    issueKind = currentTaskMode === 'rename' ? 'Rename' : 'Classify';
    createCollection(issueKind).then(async collection => {
      if (collection.success) {
        try {
          collectionUploadFile(
            record.path,
            collection.msg,
            issueKind,
            isValidEmail(email ?? '') ? email : undefined
          );
        } catch (error) {
          // openNotification(false, t('createCollectionFailed', { error: collection.msg }));
        }
      }
    });
  };

  const onClick = async (idx: number) => {
    switch (idx) {
      case 0:
        onOperation(record.path, { ...record, status: 'Processing' });
        setTaskState(tabKey, {
          applied: true,
        });
        await processBatch(
          tabKey,
          [{ path: record.path, rStatus: 'Processing', cStatus: 'Processing', name: record.name }],
          currentTaskMode
        );
        setTaskState(tabKey, {
          applied: false,
        });
        break;
      case 1: {
        apply();
        break;
      }
      case 2: {
        setOpen(true);
        // Issue logic here

        break;
      }
      case 3: {
        const newFiles = files.filter(f => f.path !== record.path);
        setTaskState(tabKey, {
          files: newFiles,
        });
        break;
      }
      default:
        break;
    }
  };

  // 处理单个批次
  async function processBatch(tabKey: string, batch: FileItem[], mode: string) {
    return new Promise(resolve => {
      const onEvent = new Channel<RenameClassify>();
      let completedCount = 0;
      onEvent.onmessage = message => {
        dealMessage(tabKey, message);
        completedCount++;
        if (completedCount === batch.length) {
          resolve(true);
        }
      };

      let targetPath = null;
      let renameTags = null;

      if (mode === 'rename') {
        renameTags = formatList;
        if (classifyFilesSameTime) {
          targetPath = directoryPath;
        }
      } else if (mode === 'classify') {
        targetPath = directoryPath;
        if (renameFilesSameTime) {
          renameTags = formatList;
        }
      }

      const onlyClassify = mode === 'classify' && !renameFilesSameTime;

      renameAndClassify(
        {
          source: batch.map(file => file.path),
          target: targetPath,
          onlyClassify,
          renameTags: renameTags || [],
        },
        onEvent
      );
    });
  }

  const dealMessage = async (tabKey: string, message: RenameClassify) => {
    const path = message.data.src;

    info(`[处理消息] 事件: ${message.event}, 路径: ${path}, 数据: ${JSON.stringify(message.data)}`);

    // 准备文件更新数据
    let fileUpdate: Partial<FileItem> = {
      progress: 100,
    };

    switch (message.event) {
      case 'rename':
        fileUpdate = {
          ...fileUpdate,
          rStatus: 'Finished',
          renaming: message.data.target,
        };
        break;

      case 'renameError':
        fileUpdate = {
          ...fileUpdate,
          rStatus: 'Failed',
          msg: message.data.errMsg,
        };
        break;

      case 'classify':
        fileUpdate = {
          ...fileUpdate,
          cStatus: 'Finished',
          classifying: message.data.target,
        };
        break;

      case 'classifyError':
        fileUpdate = {
          ...fileUpdate,
          cStatus: 'Failed',
          msg: message.data.errMsg,
        };
        break;

      default:
        return;
    }

    info(`[处理消息] 更新文件状态: ${JSON.stringify(fileUpdate)}`);
    // 更新文件状态
    updateFile(tabKey, path, fileUpdate);

    // 处理待处理文件（手动模式）
    if (message.event === 'classify' || message.event === 'rename') {
      const currentState = getTaskState(tabKey);
      const file = currentState.files.find(f => f.path === path);
      info(`[处理消息] 当前文件状态: ${JSON.stringify(file)}`);

      if (!file) return;

      let shouldAddFile = false;

      if (currentTaskMode === 'rename') {
        if (classifyFilesSameTime) {
          shouldAddFile = file.rStatus === 'Finished' && file.cStatus === 'Finished';
        } else {
          shouldAddFile = file.rStatus === 'Finished';
        }
      } else if (currentTaskMode === 'classify') {
        if (renameFilesSameTime) {
          shouldAddFile = file.cStatus === 'Finished' && file.rStatus === 'Finished';
        } else {
          shouldAddFile = file.cStatus === 'Finished';
        }
      }

      if (shouldAddFile) {
        info(`[处理消息] 添加待处理文件: ${JSON.stringify(file)}`);
        addPendingFiles(tabKey, [file]);
      }
    }
  };
  return (
    <div className="flex flex-row justify-end items-center gap-2 pr-3">
      {contextHolder}
      {items.map((item, index) => {
        const Icon = item.icon;
        let isDisabled = false;
        if (currentTaskMode === 'rename' && index == 1) {
          isDisabled = record.rStatus === 'ApplyFinished' || record.rStatus === 'Failed';
        }
        if (currentTaskMode === 'rename' && classifyFilesSameTime && index == 1) {
          isDisabled =
            (record.rStatus === 'ApplyFinished' || record.rStatus === 'Failed') &&
            (record.cStatus === 'ApplyFinished' || record.cStatus === 'Failed');
        }

        if (currentTaskMode === 'classify' && index == 1) {
          isDisabled = record.cStatus === 'ApplyFinished' || record.cStatus === 'Failed';
        }
        if (currentTaskMode === 'classify' && renameFilesSameTime && index == 1) {
          isDisabled =
            (record.rStatus === 'ApplyFinished' || record.rStatus === 'Failed') &&
            (record.cStatus === 'ApplyFinished' || record.cStatus === 'Failed');
        }

        const key = `${record.path}-${item.key}`;

        return (
          <div
            key={key}
            className={classNames(
              'flex justify-center items-center border border-[#F3F3F3] rounded w-6 h-6',
              {
                'cursor-not-allowed text-[#EBEBEB]':
                  isDisabled || (index === 2 && flightStatus === 'offline'),
                'cursor-pointer text-[#333]':
                  !isDisabled && !(index === 2 && flightStatus === 'offline'),
                'text-[#EC6B5F]': index === 3,
              }
            )}
            onClick={() => {
              onClick;
              if (index === 2 && flightStatus === 'offline') {
                return;
              }

              if (index === 0) {
                uiEvent(
                  'Click',
                  record.rStatus === 'Failed' || record.cStatus === 'Failed'
                    ? { Retry: 'No' }
                    : { Retry: 'Yes' }
                );
              }
              if (index === 1) {
                uiEvent('Click', { Apply: 'single' });
              }

              if (index === 2) {
                uiEvent(
                  'Click',
                  record.rStatus === 'Failed' || record.cStatus === 'Failed'
                    ? { Issue_report: 'Failed' }
                    : { Issue_report: 'success' }
                );
              }

              if (index === 3) {
                uiEvent('Click', { Fildhanding: 'Delete' });
              }

              if (!isDisabled) {
                onClick(index);
              }
            }}
          >
            <Tooltip
              title={
                index == 2 && flightStatus === 'offline'
                  ? t('pleaseSwitchOnlineMode')
                  : t(item.tooltip)
              }
              placement="top"
            >
              <Icon className={classNames('w-[14px] h-[14px]')} />
            </Tooltip>
          </div>
        );
      })}
      <Modal destroyOnHidden width={'460px'} open={open} closable={false} footer={null}>
        <div className="flex gap-x-3 text-[12px]">
          <IssueIcon />
          <div className="text-[#EC6B5F] text-[12px]">{t('FYI')}</div>
        </div>
        <div className="mt-[12px] text-[#333] text-[12px]">
          <div>{t('reportIssueDesc')}</div>
          <div className="mt-[25px] text-[#999]">{t('pleaseEnterEmail')}</div>
        </div>
        <Input
          className="mt-[8px]"
          onChange={v => {
            setEmail(v.target.value);
          }}
        ></Input>
        <div className="flex justify-center items-center gap-x-4 mt-4 w-full">
          <div
            className="flex justify-center items-center border border-[#EBEBEB] rounded-lg w-[100px] h-[34px] cursor-pointer"
            onClick={() => {
              setOpen(false);
              setEmail('');
            }}
          >
            {t('thingking')}
          </div>
          <div
            className={classNames(
              'flex justify-center items-center border rounded-lg w-[100px] h-[34px]',
              {
                'cursor-pointer bg-[#FFDF75]  text-[#333] border-[#EBEBEB]': true,
              }
            )}
            onClick={onReportIssue}
          >
            {t('reportIssue')}
          </div>
        </div>
      </Modal>
      <Modal destroyOnHidden width={'460px'} open={successOpen} closable={false} footer={null}>
        <div className="flex flex-col items-center">
          <IssueSuccessIcon />
          <div className="mt-[16px] font-bold text-[12px]">{t('thankyouForyour')}</div>
          <div
            className={classNames(
              'flex justify-center mt-[45px] items-center border rounded-lg w-[100px] h-[34px] cursor-pointer bg-[#FFDF75]  text-[#333] border-[#EBEBEB'
            )}
            onClick={() => {
              uiEvent('Click', { Issue_report: 'Cancel' });
              setSuccessOpen(false);
              setEmail('');
            }}
          >
            {t('thankYou')}
          </div>
        </div>
      </Modal>
    </div>
  );
});

Operation.displayName = 'Operation';

export default Operation;
