import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import EditableCell from '../EditCell';
import { useSearchParams } from 'react-router-dom';
import TruncatedText from '@/components/TruncatedText';
import ArrowIcon from '@/assets/svg/arrow-right.svg?react';
import { useTasksStore } from '@/store/tasksStore';
interface FileNameProps {
  record: any;
}

const FileName = (props: FileNameProps) => {
  const [searchParams] = useSearchParams();
  const tabKey = searchParams.get('tabKey') || '';

  const { getTaskState } = useTasksStore();
  const { currentTaskMode } = getTaskState(tabKey);

  const { t } = useTranslation();
  const { record } = props;
  const { file, rStatus, renaming } = record;

  const statusText = rStatus.toLowerCase();
  if (renaming && !(statusText.includes('processing') || statusText.includes('filed'))) {
    return (
      <div className="flex flex-col gap-y-1">
        <div className="text-[#999] text-[12px]">
          <TruncatedText title={file}>{file}</TruncatedText>
        </div>
        <div className="flex items-center gap-x-2">
          <ArrowIcon />
          <EditableCell
            tabKey={tabKey}
            recordKey={record.path}
            fieldName="renaming"
            value={record.renaming || ''}
            record={record}
          />
        </div>
      </div>
    );
  }

  if (
    currentTaskMode == 'rename' &&
    (statusText.includes('processing') || statusText.includes('failed'))
  ) {
    return (
      <div className="flex flex-col gap-y-1">
        <div className="text-[#999] text-[12px]">{file}</div>
        <div
          className={classNames('align-middle font-bold text-[12px] h-[18px]', {
            'text-[#FFB116]': statusText.includes('processing'),
            'text-[#EC6B5F]': statusText.includes('failed'),
          })}
        >
          {t(statusText)}
        </div>
      </div>
    );
  }

  return (
    <span className="font-bold text-[#333] text-[12px]">
      <TruncatedText title={file}>{file}</TruncatedText>
    </span>
  );
};

export default FileName;
