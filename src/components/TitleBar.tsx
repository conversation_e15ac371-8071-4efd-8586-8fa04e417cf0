import { useEffect, useState } from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { platform } from '@tauri-apps/plugin-os';

const appWindow = getCurrentWindow();

export default function TitleBar() {
  const [os, setOs] = useState<'mac' | 'win' | 'linux'>('mac');
  useEffect(() => {
    const p = platform();
    if (p === 'macos') setOs('mac');
    else if (p === 'windows') setOs('win');
    else setOs('linux');
  }, []);

  return (
    <div
      data-tauri-drag-region
      className="fixed top-0 left-0 w-full z-30 flex items-center h-10 px-3 drag select-none bg-[#ECECF2]"
    ></div>
  );
}
