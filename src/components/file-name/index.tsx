import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DEFAULT_FORMAT_LIST, fields } from './var';
import { CUSTOM_FIELD, SELECT_FIELD } from '@/const/localStorage';
import classNames from 'classnames';
import {
  DndContext,
  closestCenter,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  useSensor,
  useSensors,
  PointerSensor,
} from '@dnd-kit/core';
import { SortableContext, horizontalListSortingStrategy, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

function SortableTag({ tag, onRemove }: { tag: any; onRemove: () => void }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: tag.key,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    userSelect: 'none' as const,
    cursor: 'move' as const,
    opacity: isDragging ? 0 : 1,
  };

  const handleClick = (e: React.MouseEvent) => {
    if (!isDragging) {
      e.stopPropagation();
      onRemove();
    }
  };

  return (
    <span
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      onClick={handleClick}
      className={`inline-block px-3 py-1 rounded-full border text-xs font-semibold bg-white ${tag.color}`}
    >
      {tag.label}
    </span>
  );
}

function formatListToFormat(formatList: any[]) {
  return (
    <div className="flex flex-wrap gap-2">
      {formatList.map((item, index) => {
        const field = fields.find(item2 => item2.tag === item.tag);
        if (field) {
          return (
            <span key={item.tag}>
              <span
                className={`inline-block px-3 py-1 rounded-full border text-xs font-semibold bg-white ${field?.color}`}
                style={{ userSelect: 'none' }}
              >
                {field?.label}
              </span>
              {index !== formatList.length - 1 && <span className="ml-2 text-gray-500">-</span>}
            </span>
          );
        }
        return (
          <span
            key={item.tag}
            className="inline-block bg-white px-3 py-1 border rounded-full font-semibold text-xs"
          >
            {item.tag}
          </span>
        );
      })}
    </div>
  );
}

export default function FileName() {
  const { t } = useTranslation();
  const [customField, setCustomField] = useState<any[]>([DEFAULT_FORMAT_LIST]);
  const preCustomField = useRef<any[]>([DEFAULT_FORMAT_LIST]);
  const [preview, setPreview] = useState<string>('');
  const [editMode, setEditMode] = useState(false);
  const [selectedField, setSelectedField] = useState<any[]>([DEFAULT_FORMAT_LIST]);
  const [activeTag, setActiveTag] = useState<any>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const confirmDisabled = useMemo(() => {
    return customField.length === 0;
  }, [customField]);

  useEffect(() => {
    const cf = localStorage.getItem(CUSTOM_FIELD);
    if (cf) {
      try {
        const parsedFormat = JSON.parse(cf);
        setCustomField(parsedFormat);
        preCustomField.current = parsedFormat;
      } catch (error) {
        console.error('Failed to parse saved format:', error);
      }
    }

    const sf = localStorage.getItem(SELECT_FIELD);
    if (sf) {
      try {
        const parsedSelect = JSON.parse(sf);
        setSelectedField(parsedSelect);
      } catch (error) {
        console.error('Failed to parse saved select field:', error);
      }
    }
  }, []);

  useEffect(() => {
    if (customField) {
      const previewText = customField
        .map(item => {
          return item.value;
        })
        .join(' - ');
      setPreview(previewText);
    }
  }, [customField, editMode]);

  const isSelected = useMemo(() => {
    return selectedField.length === 1 && selectedField[0].tag === 'title';
  }, [selectedField]);

  const onFieldClick = (key: string) => {
    const field = fields.find(item => item.key === key);
    const tag = customField.find(item => {
      return item.tag === field?.tag;
    });

    if (tag) {
      setCustomField(prev => prev.filter(item => item.tag !== field?.tag));
      return;
    }
    if (!field) {
      return;
    }
    setCustomField(prev => [...prev, { ...field, zhCn: true }]);
  };

  // 重置为默认格式
  const onEditReset = () => {
    setCustomField([]);
  };

  const onEditCancel = () => {
    setEditMode(false);

    setCustomField(preCustomField.current);
  };
  // I want this 按钮点击事件（可自定义逻辑）
  const onEditOk = () => {
    if (confirmDisabled) {
      return;
    }
    preCustomField.current = [...customField];
    localStorage.setItem(CUSTOM_FIELD, JSON.stringify(customField));
    if (!isSelected) {
      setSelectedField(customField);
      localStorage.setItem(SELECT_FIELD, JSON.stringify(customField));
    }
    setEditMode(false);
  };

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const draggedTag = customField.find(tag => tag.key === active.id);
    if (draggedTag) {
      setActiveTag(draggedTag);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTag(null);

    if (over && active.id !== over.id) {
      const oldIndex = customField.findIndex(tag => tag.key === active.id);
      const newIndex = customField.findIndex(tag => tag.key === over.id);

      const newCustomField = [...customField];
      const [movedTag] = newCustomField.splice(oldIndex, 1);
      newCustomField.splice(newIndex, 0, movedTag);

      setCustomField(newCustomField);
    }
  };

  return (
    <>
      <div className="mb-2 font-bold text-xl">{t('chooseNamingFormat')}</div>
      {editMode && (
        <div>
          <div className="relative mb-2 w-full">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
            >
              <div className="flex flex-wrap gap-2 bg-gray-50 mt-2 p-2 border rounded min-h-11 text-gray-800 text-base">
                <SortableContext
                  items={customField.map(tag => tag.key)}
                  strategy={horizontalListSortingStrategy}
                >
                  {customField.map(tag => (
                    <SortableTag key={tag.key} tag={tag} onRemove={() => onFieldClick(tag.key)} />
                  ))}
                </SortableContext>
              </div>
              <DragOverlay>
                {activeTag ? (
                  <span
                    className={`inline-block px-3 py-1 rounded-full border text-xs font-semibold bg-white ${activeTag.color}`}
                  >
                    {activeTag.label}
                  </span>
                ) : null}
              </DragOverlay>
            </DndContext>
            <div className="flex justify-end gap-2 mt-2 w-full">
              <div
                className={classNames(
                  'bg-gradient-to-r from-[#FFD45D] to-[#FFEB99] text-sm px-2 py-1 rounded-md',
                  {
                    'cursor-not-allowed opacity-50': confirmDisabled,
                    'cursor-pointer': !confirmDisabled,
                  }
                )}
                onClick={onEditOk}
              >
                {t('iWantThis')}
              </div>
              <div
                className="bg-[#F3F4F6] px-2 py-1 rounded-md text-gray-700 text-sm cursor-pointer"
                onClick={onEditCancel}
              >
                {t('cancel')}
              </div>
              <div
                className="bg-green-600 px-2 py-1 rounded-md text-white text-sm cursor-pointer"
                onClick={onEditReset}
              >
                {t('reset')}
              </div>
            </div>
          </div>
          <div className="mt-4 text-gray-700 text-base">{t('clickToInsert')}</div>
          <div className="flex flex-wrap gap-2 mt-2">
            {fields.map(field => {
              // if (isTagSelected(field.tag)) {
              //   return null; // 如果该标签已被选中，则不显示
              // }
              return (
                <span
                  key={field.key}
                  className={classNames(
                    `inline-block px-3 py-1 rounded-full border text-xs font-semibold bg-white cursor-pointer`,
                    {
                      'border-gray-500': customField.some(item => item.key === field.key),
                      'text-gray-500': customField.some(item => item.key === field.key),
                      [field.color]: !customField.some(item => item.key === field.key),
                    }
                  )}
                  style={{ userSelect: 'none' }}
                  onClick={() => onFieldClick(field.key)}
                >
                  {field.label}
                </span>
              );
            })}
          </div>
          <div className="mt-6 mb-1 text-gray-700">{t('formatPreview')}</div>
          <div
            className="bg-gray-50 p-2 border rounded text-gray-800 text-base"
            style={{ minHeight: 48, maxHeight: 80, overflow: 'auto' }}
          >
            {preview || ' '}
          </div>
        </div>
      )}
      {!editMode && (
        <div
          className="bg-white p-2 border rounded text-gray-800 text-base"
          style={{ overflow: 'auto' }}
        >
          <div
            className={`text-base text-gray-800 mb-2 p-4 rounded-md flex items-center justify-between ${
              isSelected ? 'bg-[#F3F4F6]' : 'bg-white'
            }`}
            onClick={() => {
              setSelectedField([DEFAULT_FORMAT_LIST]);
              localStorage.setItem(SELECT_FIELD, JSON.stringify([DEFAULT_FORMAT_LIST]));
            }}
          >
            <div>{formatListToFormat([DEFAULT_FORMAT_LIST])}</div>

            <div
              className={classNames('w-6 h-6 shrink-0 rounded-full bg-white', {
                'border border-[#E0E0E0]': !isSelected,
                'border-[5px] border-[#FFC117]': isSelected,
              })}
            ></div>
          </div>
          {!(customField.length == 1 && customField[0].tag === 'title') && (
            <div
              className={`text-base text-gray-800 p-4 rounded-md flex items-center justify-between ${
                isSelected ? 'bg-white' : 'bg-[#F3F4F6]'
              }`}
              onClick={() => {
                setSelectedField(customField);
                localStorage.setItem(SELECT_FIELD, JSON.stringify(customField));
              }}
            >
              {formatListToFormat(customField)}
              <div
                className={classNames('w-6 h-6 shrink-0 rounded-full bg-white', {
                  'border border-[#E0E0E0]': isSelected,
                  'border-[5px] border-[#FFC117]': !isSelected,
                })}
              ></div>
            </div>
          )}

          <div
            className="flex justify-center items-center mt-20 mb-10 text-[#FFC117] cursor-pointer"
            onClick={() => setEditMode(true)}
          >
            {t('editFormat')}
          </div>
        </div>
      )}
    </>
  );
}
