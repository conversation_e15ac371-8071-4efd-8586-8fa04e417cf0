import { message, Switch } from 'antd';
import FlightIcon from '@/assets/svg/flight.svg?react';
import { useDispatch, useSelector } from 'react-redux';
import { flightActions } from '@/store/slices/flight';
import { RootState } from '@/store';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { uiEvent } from '@/api/api';
import { getPlatformType } from '@/utils/string';

interface FlightSwitchProps {
  isInSidebar?: boolean;
}

const FlightSwitch = ({ isInSidebar = false }: FlightSwitchProps): JSX.Element => {
  const { t } = useTranslation();
  const flightData = useSelector((state: RootState) => state.flight.flightData);
  const settings = useSelector((state: RootState) => state.settings);
  const { selectedModel, renameModels, classifyModels } = settings;
  const { status } = flightData;
  const dispatchRTK = useDispatch();
  const isOffline = status === 'offline';
  const os = getPlatformType();

  const hasOnlineModel = useMemo(() => {
    const hasEmbeddingModel = renameModels.some(model => model.status == 'installed');
    const hasRenameModel = classifyModels.some(model => model.status === 'installed');
    return hasEmbeddingModel && hasRenameModel;
  }, [renameModels, classifyModels]);

  const isSelectedModelValid = useMemo(() => {
    if (!selectedModel) return false;
    const isLocalEmbedding = selectedModel?.embedding?.type === 'Local';
    const isLocalRename = selectedModel?.rename?.type === 'Local';

    return isLocalEmbedding && isLocalRename;
  }, [selectedModel]);

  const handleSwitchChange = useCallback(
    (checked: boolean) => {
      if (!isOffline && !hasOnlineModel) {
        message.open({
          key: 'modelsWarning',
          type: 'warning',
          content: t('pleaseDownloadModels'),
        });
        return;
      }

      if (!isOffline && !isSelectedModelValid) {
        message.open({
          key: 'modelsWarning',
          type: 'warning',
          content: t('pleaseSwitchOfflineModels'),
        });
        return;
      }

      uiEvent('Click', checked ? { Privacy: 'Offline' } : { Privacy: 'Online' });
      dispatchRTK(
        flightActions.updateFlightData({
          status: checked ? 'offline' : 'online',
          flightMode: checked,
        })
      );
    },
    [dispatchRTK, isOffline, hasOnlineModel, t, isSelectedModelValid]
  );

  // 侧边栏样式（Windows 平台）
  if (isInSidebar || os === 'win') {
    return (
      <div className="group mb-[70px] w-fit -ml-3">
        <div
          className="flex items-center group-hover:gap-x-2 bg-white p-2 border border-[#EBEBEB] w-fit overflow-hidden transition-all duration-300 ease-in-out"
          style={{ borderRadius: '0px 14px 14px 0px' }}
        >
          <FlightIcon className="flex-shrink-0" />
          <div className="hidden group-hover:block group-hover:ml-2 text-[#333] text-xs whitespace-nowrap">
            {isOffline ? t('offlineModeOpen') : t('onlineModeClose')}
          </div>
          <Switch
            checked={isOffline}
            onChange={handleSwitchChange}
            size="small"
            className="hidden group-hover:block group-hover:ml-2"
          />
        </div>
      </div>
    );
  }

  // 原来的右上角样式（Mac 平台）
  return (
    <div className="group top-0 right-3 z-50 absolute flex items-center group-hover:gap-x-2 bg-white p-2 hover:pt-5 border border-[#EBEBEB] rounded-b-[14px] w-fit overflow-hidden transition-all duration-300 ease-in-out">
      <FlightIcon className="flex-shrink-0" />
      <div className="hidden group-hover:block group-hover:ml-2 text-[#333] text-xs whitespace-nowrap">
        {isOffline ? t('offlineModeOpen') : t('onlineModeClose')}
      </div>
      <Switch
        checked={isOffline}
        onChange={handleSwitchChange}
        size="small"
        className="hidden group-hover:block group-hover:ml-2"
      />
    </div>
  );
};

export default FlightSwitch;
