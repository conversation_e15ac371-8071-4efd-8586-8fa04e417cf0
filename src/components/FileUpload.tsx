import { useState, useEffect, useRef } from 'react';
import { open } from '@tauri-apps/plugin-dialog';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';
import { listen } from '@tauri-apps/api/event';
import { stat, readDir } from '@tauri-apps/plugin-fs';
import { join } from '@tauri-apps/api/path';
import { logInfo, logError } from '@/utils/logger';
import type { FileItem } from '@/types/file';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { useSearchParams } from 'react-router-dom';
import { info } from '@tauri-apps/plugin-log';
import { motion, AnimatePresence } from 'framer-motion';
import UploadIcon from '@/assets/svg/upload.svg?react';
import DelIcon from '@/assets/svg/delete.svg?react';
import SelectIcon from '@/assets/svg/select-button.svg?react';
import { useDispatch, useSelector } from 'react-redux';
import { dragActions } from '@/store/slices/drag';
import { RootState } from '@/store';
import { getFileName } from '@/utils/string';
const appWindow = getCurrentWindow();
const extensions = ['pdf', 'docx', 'png', 'jpeg', 'jpg'];
type Props = {
  onFileSelected?: (files: FileItem[]) => void;
  files?: FileItem[];
};

const truncateFileName = (name: string, maxLength: number = 40) => {
  if (name.length <= maxLength) return name;
  const extension = name.split('.').pop();
  const nameWithoutExt = name.slice(0, name.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.slice(0, maxLength - 3) + '..';
  return `${truncatedName}.${extension}`;
};

export default function FileUpload({ onFileSelected, files }: Props) {
  const { t } = useTranslation();
  const [filePath, setFilePath] = useState<FileItem[]>(files || []);
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  const fileListRef = useRef<HTMLDivElement>(null);
  const [searchParams] = useSearchParams();
  const nopCache = searchParams.get('nopCache') === 'true';
  const dispatchRTK = useDispatch();
  const isDragging = useSelector((state: RootState) => state.drag.isDragging);

  // 监听 nopCache 参数变化
  useEffect(() => {
    info(`监听 nopCache 参数变化: ${nopCache}`);
    if (nopCache) {
      setFilePath([]);
      onFileSelected?.([]);
    }
  }, [nopCache]);

  useEffect(() => {
    const dragRegion = document.getElementById('titlebar-drag-region');
    if (dragRegion) {
      dragRegion.addEventListener('mousedown', () => {
        appWindow.startDragging();
      });
    }
  }, []);

  const handleSelectFile = async (isAddFile: boolean = false) => {
    const selected = await open({
      multiple: true,
      directory: false,
      filters: [
        {
          name: 'Documents',
          extensions,
        },
      ],
    });

    if (Array.isArray(selected)) {
      if (isAddFile) {
        const files = selected.map(path => ({
          id: Math.random().toString(36).substr(2, 9),
          path,
          name: getFileName(path),
          size: 0,
          type: path.split('.').pop() || '',
          rStatus: 'Waiting' as const,
          cStatus: 'Waiting' as const,
        }));
        setFilePath(prev => [...prev, ...files]);
      } else {
        const files = selected.map(path => ({
          id: Math.random().toString(36).substr(2, 9),
          path,
          name: getFileName(path),
          size: 0,
          type: path.split('.').pop() || '',
          rStatus: 'Waiting' as const,
          cStatus: 'Waiting' as const,
        }));
        setFilePath(files);
      }
    }
  };

  useEffect(() => {
    onFileSelected?.(filePath);
    if (fileListRef.current) {
      fileListRef.current.scrollTop = fileListRef.current.scrollHeight;
    }
  }, [filePath]);

  useEffect(() => {
    const unListen = listen('tauri://drag-drop', async event => {
      logInfo(`[Tauri] 文件拖入事件触发：${event}`);
      logInfo(`isDragging: ${isDragging}`);
      if (!isDragging) {
        return;
      }
      dispatchRTK(dragActions.setDragging(false));
      // 获取文件信息
      for (const path of (event.payload as { paths: string[] }).paths) {
        const info = await stat(path);
        if (info.isDirectory) {
          logInfo(`[Tauri] 拖入的是文件夹: ${path}`);
          // 如何获取文件夹中的所有文件
          const files = await readDir(path);
          logInfo(`[Tauri] 文件夹中的文件：${files}`);
          const fileList = await Promise.all(
            files.map(async file => {
              const fullPath = await join(path, file.name);
              const fileInfo = await stat(fullPath);
              const fileType = file.name.split('.').pop()?.toLowerCase() || '';
              if (!extensions.includes(fileType)) {
                message.warning(`${file.name} ${t('unsupportedFileType')}`);
                return null;
              }
              return {
                id: Math.random().toString(36).substr(2, 9),
                path: fullPath,
                name: file.name,
                size: fileInfo.size,
                type: fileType,
              };
            })
          );
          // 过滤掉不支持的文件类型
          const validFiles = fileList.filter(f => f !== null) as FileItem[];
          // 如果文件存在，则不添加
          const newFiles = validFiles.filter(f => !filePath.some(f2 => f2.path === f.path));
          setFilePath(prev => [...prev, ...newFiles]);
        } else if (info.isFile) {
          logInfo(`[Tauri] 拖入的是文件: ${path}`);
          const fileType = path.split('.').pop()?.toLowerCase() || '';
          if (!extensions.includes(fileType)) {
            message.warning(`${getFileName(path)} ${t('unsupportedFileType')}`);
            return;
          }
          const file: FileItem = {
            id: Math.random().toString(36).substr(2, 9),
            path,
            name: getFileName(path),
            size: info.size,
            type: fileType,
            rStatus: 'Waiting',
            cStatus: 'Waiting',
          };
          // 如果文件存在，则不添加
          if (!filePath.some(f => f.path === path)) {
            setFilePath(prev => [...prev, file]);
          } else {
            message.warning(t('fileAlreadyExist'));
            logInfo(`[Tauri] 文件已存在: ${path}`);
          }
        } else {
          logError(`[Tauri] 未知类型: ${path}`);
        }
      }
    });

    const unListenCancel = listen('tauri://drag-leave', () => {
      dispatchRTK(dragActions.setDragging(false));
    });

    const unListenDragOver = listen('tauri://drag-over', (e: any) => {
      const { position } = e.payload;
      if (position) {
        const x = Math.abs(position.x);
        const el = document.getElementById('menu');
        if (el) {
          const rect = el.getBoundingClientRect();
          if (x > rect.right) {
            dispatchRTK(dragActions.setDragging(true));
          } else {
            dispatchRTK(dragActions.setDragging(false));
          }
        }
      }
    });

    return () => {
      unListen.then(fn => fn());
      unListenCancel.then(fn => fn());
      unListenDragOver.then(fn => fn());
    };
  }, [isDragging, filePath, dispatchRTK, t]);

  return (
    <div className="mx-auto w-full">
      <div className="flex flex-col justify-center items-center">
        <AnimatePresence mode="wait">
          {filePath.length == 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.2 }}
              onClick={() => handleSelectFile(false)}
              className={`flex flex-col items-center justify-center border border-dashed rounded-[8px] border-[#FFDF75] w-full h-[142px]`}
            >
              <motion.div initial={{ y: 10 }} animate={{ y: 0 }} transition={{ duration: 0.2 }}>
                <UploadIcon />
              </motion.div>
              <motion.p
                initial={{ y: 10, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.2, delay: 0.1 }}
                className="text-[#333] text-[12px]"
              >
                {t('dragFiles')}
              </motion.p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      <AnimatePresence>
        {filePath.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col gap-1 mt-4 text-gray-700 text-sm break-all"
          >
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.1 }}
              className="pt-[24px] pb-[16px] font-bold text-[#333] text-[14px]"
            >
              {t('selectedFiles')}
            </motion.div>
            <motion.div
              ref={fileListRef}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="flex flex-col gap-1 px-[24px] py-[4px] border-[#EBEBEB] border-[1px] rounded-[8px] max-h-[300px] overflow-y-auto"
            >
              <AnimatePresence>
                {filePath.map((file, idx) => (
                  <motion.div
                    key={file.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className={`flex items-center gap-1 justify-between py-[8px] border-[#EBEBEB] ${
                      idx !== filePath.length - 1 ? 'border-b-[1px]' : ''
                    }`}
                  >
                    <div title={file.name}>{truncateFileName(file.name)}</div>
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <DelIcon
                        onClick={() => {
                          setFilePath(filePath.filter(f => f.id !== file.id));
                        }}
                        className="w-[12px] h-[12px] text-[#333] cursor-pointer"
                      />
                    </motion.div>
                  </motion.div>
                ))}
              </AnimatePresence>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="flex justify-between items-center mt-[8px] text-[#333]"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => handleSelectFile(true)}
                className="flex items-center gap-x-[10px] bg-[#FFDD6E] px-[8px] rounded-[8px] h-[30px]"
              >
                <SelectIcon />
                <span>{t('dragFilesSimple')}</span>
              </motion.div>
              <div className="flex items-center gap-x-[10px] text-[12px]">
                <div className="text-[#999]">
                  {filePath.length} {t('unit')}
                </div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    setFilePath([]);
                  }}
                  className="text-[#EC6B5F] cursor-pointer"
                >
                  {t('clearAll')}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
