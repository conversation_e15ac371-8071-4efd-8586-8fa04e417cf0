import { RootState } from '@/store';
import { dirActions } from '@/store/slices/dir';
import { Progress, Tooltip } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import EmbeddingIcon from '@/assets/svg/embeding.svg?react';
import styles from './index.module.scss';
import DoneIcon from '@/assets/svg/done.svg?react';
import QuestionMarkIcon from '@/assets/svg/question-mark.svg?react';

interface EmbeddingProps {
  autoConfirmDelay?: number; // 自动确认延迟时间（毫秒），默认 3000ms
}

const Embedding = ({ autoConfirmDelay = 3000 }: EmbeddingProps = {}): JSX.Element | null => {
  const dir = useSelector((state: RootState) => state.dir);
  const { show: embeddingShow, embeddingData } = dir;
  const dispatchRTK = useDispatch();
  const { t } = useTranslation();

  // 状态文本数组
  const statusTexts = [
    'analyzingTargetDirectoryStructure',
    'analyzingSubfolderAttributes',
    'analyzingFileContent',
    'extractingFileFeatures',
  ];

  const [currentStatusIndex, setCurrentStatusIndex] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const autoNextTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const countdownTimerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const handleClose = useCallback(() => {
    dispatchRTK(dirActions.toggleEmbedding(false));
  }, [dispatchRTK]);

  // 自动确认和进入下一步的逻辑
  useEffect(() => {
    // 清除之前的定时器
    if (autoNextTimerRef.current) {
      clearTimeout(autoNextTimerRef.current);
      autoNextTimerRef.current = null;
    }
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }

    // 当目录解析完成时，开始倒计时
    if (embeddingData.progress >= 100 && embeddingShow) {
      // 计算倒计时秒数（从毫秒转换为秒）
      const countdownSeconds = Math.ceil(autoConfirmDelay / 1000);
      setCountdown(countdownSeconds);

      // 开始倒计时
      countdownTimerRef.current = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            // 倒计时结束，执行自动确认
            if (countdownTimerRef.current) {
              clearInterval(countdownTimerRef.current);
              countdownTimerRef.current = null;
            }

            // 直接调用现有的 handleClose 函数
            handleClose();
            // 延迟一点时间后直接调用开始函数
            setTimeout(() => {
              // 根据当前路径调用对应的开始函数
              const currentPath = window.location.pathname;
              if (currentPath === '/rename' && (window as any).handleRenameStart) {
                (window as any).handleRenameStart();
              } else if (currentPath === '/classify' && (window as any).handleClassifyStart) {
                (window as any).handleClassifyStart();
              }
            }, 300);

            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      // 重置倒计时
      setCountdown(0);
    }

    return () => {
      if (autoNextTimerRef.current) {
        clearTimeout(autoNextTimerRef.current);
        autoNextTimerRef.current = null;
      }
      if (countdownTimerRef.current) {
        clearInterval(countdownTimerRef.current);
        countdownTimerRef.current = null;
      }
    };
  }, [embeddingData.progress, embeddingShow, dispatchRTK, autoConfirmDelay, handleClose]);

  useEffect(() => {
    if (!embeddingShow) {
      setCurrentStatusIndex(0);
      setIsCompleted(false);
      return;
    }

    let timeoutId: ReturnType<typeof setTimeout>;

    const switchToNextStatus = () => {
      setCurrentStatusIndex(prev => {
        const nextIndex = prev + 1;

        if (nextIndex >= statusTexts.length - 1) {
          setIsCompleted(true);
          return statusTexts.length - 1;
        }

        const randomDelay = Math.random() * 2000 + 2000;
        timeoutId = setTimeout(switchToNextStatus, randomDelay);

        return nextIndex;
      });
    };

    if (embeddingData.progress >= 100 || embeddingData.progress > 15) {
      setCurrentStatusIndex(statusTexts.length - 1);
      setIsCompleted(true);
      return;
    }

    if (!isCompleted && currentStatusIndex < statusTexts.length - 1) {
      const randomDelay = Math.random() * 2000 + 2000;
      timeoutId = setTimeout(switchToNextStatus, randomDelay);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [embeddingShow, embeddingData.progress, isCompleted, currentStatusIndex, statusTexts.length]);

  return (
    <AnimatePresence>
      {embeddingShow && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="top-0 right-0 bottom-0 left-0 z-50 absolute flex justify-center items-center bg-transparent w-full h-full"
        >
          <div className="top-0 left-0 z-0 absolute bg-[#FFFFFF99] blur-md w-full h-full"></div>
          <motion.div
            initial={{ scale: 0.9, y: -20, opacity: 0 }}
            animate={{ scale: 1, y: 0, opacity: 1 }}
            exit={{ scale: 0.9, y: -20, opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: 'easeInOut',
              scale: { type: 'spring', stiffness: 300, damping: 25 },
            }}
            className="z-10 flex flex-col items-center bg-white shadow-[0px_0px_25px_0px_rgba(0,0,0,0.1)] p-5 px-12 border border-[#ebebeb] rounded-xl w-[336px]"
          >
            <EmbeddingIcon></EmbeddingIcon>
            <div className="mt-3 mb-3 font-bold text-[#333] text-xs">
              {embeddingData.progress >= 100 ? (
                t('directoryParsingCompleted')
              ) : (
                <div className="flex items-center gap-x-2">
                  <span> {t('weParsingDirectory')}</span>
                  <Tooltip title={t('directorySelectedTips')}>
                    <QuestionMarkIcon className="cursor-pointer" />
                  </Tooltip>
                </div>
              )}
            </div>
            <Progress showInfo={false} percent={embeddingData.progress}></Progress>
            <div className="relative mt-3 overflow-hidden text-xs">
              {embeddingData.progress >= 100 ? (
                <DoneIcon />
              ) : (
                <span className={classNames('inline-block', styles['text-marquee'])}>
                  {t(statusTexts[currentStatusIndex])}
                </span>
              )}
            </div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.15 }}
              className={classNames(
                'mt-6 px-6 py-1 border text-xs  text-[#333] rounded-lg cursor-pointer',
                { 'border-[#EBEBEB]': embeddingData.progress < 100 },
                { 'border-[#FFDF75] bg-[#FFDF75]': embeddingData.progress >= 100 }
              )}
              onClick={handleClose}
            >
              {embeddingData.progress >= 100
                ? countdown > 0
                  ? `${t('confirm')} (${countdown})`
                  : t('confirm')
                : t('cancel')}
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
export default Embedding;
