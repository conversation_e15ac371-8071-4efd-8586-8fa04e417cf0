import React, { useState } from 'react';

interface IconComponentProps extends React.HTMLAttributes<HTMLElement> {
  iconName: string;
}

function IconComponent(props: IconComponentProps) {
  const { iconName, ...restProps } = props;
  const [Icon, setIcon] = useState<React.ComponentType | null>(null);
  React.useEffect(() => {
    const loadIcon = async () => {
      try {
        // 动态导入 SVG
        const module = await import(`@/assets/svg/${iconName}.svg?react`);
        setIcon(() => module.default);
      } catch (error) {
        console.error(`Failed to load icon: ${iconName}`, error);
      }
    };

    loadIcon();
  }, [iconName]);

  if (!Icon) return <span></span>;
  return <Icon {...restProps} />;
}

export default IconComponent;
