import LaunchIcon from '@/assets/svg/launch.svg?react';
import { Trans, useTranslation } from 'react-i18next';
import { useTabsContext } from '@tauri-apps/plugin-tabs';
import { useLayoutEffect, useState } from 'react';
import { SKIP_LAUNCH } from '@/const/localStorage';
const Launch = () => {
  const { open } = useTabsContext();
  const { t } = useTranslation();
  const [visible, setVisible] = useState(true);

  useLayoutEffect(() => {
    const skipLaunch = localStorage.getItem(SKIP_LAUNCH);
    if (skipLaunch && skipLaunch != '') {
      setVisible(false);
    }
  }, []);

  return (
    <>
      {visible && (
        <div className="top-0 left-0 z-50 absolute flex flex-col justify-center items-center bg-white w-full h-full">
          <LaunchIcon />
          <div className="pt-[10px] pb-[20px] font-bold text-[20px]">
            {t('DownloadOfflineModels')}
          </div>
          <div>
            <div className="bg-[#FAFAFA] px-[24px] py-[22px] border-[#EBEBEB] rounded-[8px] max-w-[480px] overflow-hidden text-[14px] whitespace-pre-wrap">
              <Trans
                i18nKey={'toProtect'}
                t={t}
                components={{
                  1: (
                    <span
                      onClick={() => {
                        setVisible(false);
                        localStorage.setItem(SKIP_LAUNCH, 'true');
                        open({
                          id: 'setting',
                          title: 'setting',
                          path: 'setting',
                        });
                      }}
                      className="font-semibold text-[#F98E47] cursor-pointer"
                    ></span>
                  ),
                }}
              ></Trans>
            </div>
          </div>
          <div className="flex gap-x-[16px] mt-[40px] text-[#333] text-[12px]">
            <div
              onClick={() => {
                setVisible(false);
                localStorage.setItem(SKIP_LAUNCH, 'true');
              }}
              className="px-[18px] py-[8px] border border-[#EBEBEB] rounded-[8px] cursor-pointer"
            >
              {t('skip')}
            </div>
            <div
              className="bg-[#FFDF75] px-[18px] py-[8px] border border-[#FFF7DB] rounded-[8px] cursor-pointer"
              onClick={() => {
                setVisible(false);
                localStorage.setItem(SKIP_LAUNCH, 'true');
                open({
                  id: 'setting',
                  title: 'setting',
                  path: 'setting',
                });
              }}
            >
              {t('download')}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Launch;
