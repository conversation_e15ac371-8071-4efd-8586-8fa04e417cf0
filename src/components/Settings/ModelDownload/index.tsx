import { Checkbox, message, notification, Radio, Tooltip } from 'antd';
import { modelTypeColumn } from './var';
import { useCallback, useEffect, useRef, useState } from 'react';
import { cloneDeep, debounce } from 'lodash';
import { AppDispatch, RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { downloadModel, getDownloadProgress, removeModel, switchModel, uiEvent } from '@/api/api';
import { AiModelsBean } from '@/api/models/AiModelsBean';
import classNames from 'classnames';
import { motion, AnimatePresence } from 'framer-motion';
import DeleteIcon from '@/assets/svg/delete.svg?react';
import DownloadIcon from '@/assets/svg/download.svg?react';
import LoadingIcon from '@/assets/svg/loading.svg?react';
import { fetchModels, fetchSelectedModel, settingsActions } from '@/store/slices/settings';
import { useTranslation } from 'react-i18next';
import { error, info } from '@tauri-apps/plugin-log';
import { formatBytesUp } from '@/utils/byte';

const ModelDownload = () => {
  const [api, contextHolder] = notification.useNotification();

  const selectedModelRef = useRef<typeof selectedModel | null | undefined>(null);
  const hasInitSelectedModel = useRef(false);

  const [reTag, setReTag] = useState('Online');
  const [claTag, setClaTag] = useState('Online');
  const settings = useSelector((state: RootState) => state.settings);
  const { selectedModel, renameModels, classifyModels, renameDownloading, classifyDownloading } =
    settings;
  const flight = useSelector((state: RootState) => state.flight.flightData);
  const { status: flightStatus } = flight;

  const { t } = useTranslation();

  const dispatchRTK = useDispatch<AppDispatch>();
  useEffect(() => {
    dispatchRTK(fetchModels());
  }, []);

  useEffect(() => {
    if ((renameModels.length > 0, classifyModels.length > 0)) {
      dispatchRTK(fetchSelectedModel());
    }
  }, [renameModels, classifyModels, dispatchRTK]);

  useEffect(() => {
    selectedModelRef.current = selectedModel;
  }, [selectedModel]);

  useEffect(() => {
    if (
      selectedModel &&
      selectedModel?.rename &&
      selectedModel?.embedding &&
      !hasInitSelectedModel.current
    ) {
      hasInitSelectedModel.current = true;

      if (selectedModel?.rename?.type === 'Local') {
        setReTag('Offline');
      } else {
        setReTag('Online');
      }

      if (selectedModel.embedding.type === 'Local') {
        setClaTag('Offline');
      } else {
        setClaTag('Online');
      }
    }
  }, [selectedModel]);

  const onCheckboxChange = async (mod: 'rename' | 'classify', type: 'Offline' | 'Online') => {
    if (mod === 'rename') {
      if (type === 'Offline') {
        uiEvent('Rename_offline', { Rename: 'offline' });
      } else {
        uiEvent('Rename_online', { Rename: 'online' });
      }
      setReTag(type);
    }
    if (mod === 'classify') {
      if (type === 'Offline') {
        uiEvent('Folder_offline', { Folder: 'offline' });
      } else {
        uiEvent('Folder_online', { Folder: 'online' });
      }
      setClaTag(type);
    }
    const req = cloneDeep(selectedModel);
    if (type === 'Online') {
      if (!req) return;
      if (mod == 'rename') {
        req.rename = { type: 'Online' };
      } else {
        req.embedding = { type: 'Online' };
      }
      await switchModel(req);
      dispatchRTK(settingsActions.setSelectedModel(req));
    }
  };

  const debouncedSwitchOrDownLoadRenameModel = useCallback(
    debounce((mod: 'rename' | 'classify', model: AiModelsBean) => {
      switchOrDownLoadRenameModel(mod, model);
    }, 300),
    [selectedModel]
  );

  const switchOrDownLoadRenameModel = async (mod: 'rename' | 'classify', model: AiModelsBean) => {
    if (model.status === 'installed') {
      const req = cloneDeep(selectedModelRef.current);
      if (!req) return;
      // 已安装,切换
      if (mod === 'rename') {
        req.rename = {
          type: 'Local',
          data: {
            name: model.full_name,
            sha256: model.sha256,
            size: model.size,
            type: model.type,
          },
        };
      }

      if (mod === 'classify') {
        req.embedding = {
          type: 'Local',
          data: {
            name: model.full_name,
            sha256: model.sha256,
            size: model.size,
            type: model.type,
          },
        };
      }
      await switchModel(req);
      dispatchRTK(settingsActions.setSelectedModel(req));
      return;
    }
    // 先下载模型，再切换
    if (mod === 'rename') {
      if (renameDownloading?.[model.full_name]) return;
    }

    if (mod === 'classify') {
      if (classifyDownloading?.[model.full_name]) return;
    }

    const downloadResult = await downloadModel([{ name: model.name, type: model.type }]);
    if (!downloadResult.success || downloadResult.data.length == 0) {
      message.error(t('downloadFailed'));
      info(`下载失败`);
      return;
    }
    const uuid = downloadResult.data[0].uuid;
    let polling = true; // 控制是否继续轮询
    async function pollDownloadProgress() {
      if (!polling) return;
      const downloadProgress = await getDownloadProgress([uuid]);
      info(`downloadProgress: ${JSON.stringify(downloadProgress)}`);
      if (!downloadProgress.success || downloadProgress.data.length == 0) {
        polling = false;
        message.error(t('downloadFailed'));
        return;
      }

      // 下载完成
      if (downloadProgress.data[0].status.type == 'Done') {
        info(`下载完成`);
        polling = false;
        // sleep 1s
        await new Promise(resolve => setTimeout(resolve, 1000));
        const req = cloneDeep(selectedModelRef.current);
        if (!req) return;
        if (mod == 'rename') {
          req.rename = {
            type: 'Local',
            data: {
              name: model.full_name,
              sha256: model.sha256,
              size: model.size,
              type: model.type,
            },
          };
        } else {
          req.embedding = {
            type: 'Local',
            data: {
              name: model.full_name,
              sha256: model.sha256,
              size: model.size,
              type: model.type,
            },
          };
        }
        await switchModel(req);
        openNotification(false, t('switchModelSuccess'));
        dispatchRTK(settingsActions.setSelectedModel(req));
        dispatchRTK(
          settingsActions.delDownloading({
            type: mod,
            data: model.full_name,
          })
        );
        dispatchRTK(
          settingsActions.addInstall({
            type: mod,
            data: model.full_name,
          })
        );
      } else {
        // 更新进度值
        const progress = Math.floor(
          (downloadProgress.data[0].status.content / downloadProgress.data[0].total) * 100
        );
        info(`更新进度值 ${progress}`);
        dispatchRTK(
          settingsActions.addDownloading({
            type: mod,
            data: { [model.full_name]: progress },
          })
        );
      }
      if (polling) {
        setTimeout(pollDownloadProgress, 1000);
      }
    }
    info(`轮询下载进度`);
    pollDownloadProgress();
  };

  const openNotification = (pauseOnHover: boolean, message?: string, description?: string) => {
    api.open({
      message: message || '',
      description: description || '',
      pauseOnHover,
    });
  };

  const deleteModel = async (
    mod: 'rename' | 'classify',
    isDelSelect: boolean,
    model: AiModelsBean
  ) => {
    const result = await removeModel([{ name: model.full_name, type: model.type }]);
    if (!result.success) {
      error(`删除模型失败 ${model.full_name} ${model.type} 错误：${result.message}`);
      openNotification(false, t('deleteModelFailed'));
      return;
    }

    if (result.success) {
      dispatchRTK(
        settingsActions.delInstall({
          type: mod,
          data: model.full_name,
        })
      );

      if (isDelSelect) {
        const req = cloneDeep(selectedModelRef.current);
        if (!req) return;
        if (mod == 'rename') {
          req.rename = { type: 'Online' };
        } else {
          req.embedding = { type: 'Online' };
        }
        await switchModel(req);
        dispatchRTK(settingsActions.setSelectedModel(req));
      }
    }
  };

  return (
    <div className="mt-[40px]">
      {contextHolder}
      <div className="min-w-40 font-bold text-[#333] text-[14px]">{t('modelSelection')}</div>
      <div className="flex mt-[30px]">
        <div className="min-w-40 text-[#333] text-[12px]">{t('renaming')}</div>
        <div>
          <div>
            {modelTypeColumn.map(item => {
              const isOnlineDisabled = flight.status === 'offline' && item.value === 'Online';
              return (
                <Tooltip
                  key={item.value}
                  title={isOnlineDisabled ? t('offlineModeOnlineDisabled') : ''}
                  placement="top"
                >
                  <Checkbox
                    value={item.value}
                    checked={reTag == item.value}
                    disabled={isOnlineDisabled}
                    onChange={e => onCheckboxChange('rename', e.target.value)}
                  >
                    <div
                      className={classNames(
                        'text-[#333] pl-[12px] w-48 flex items-center h-[34px] rounded-[8px] border text-[12px]',
                        {
                          'border-[#EBEBEB]': reTag !== item.value,
                          'border-[#FFDF75] bg-[#FFF9E5]': reTag === item.value,
                          'opacity-50 cursor-not-allowed': isOnlineDisabled,
                        }
                      )}
                    >
                      {t(item.title)}
                    </div>
                  </Checkbox>
                </Tooltip>
              );
            })}
          </div>
          <AnimatePresence>
            {reTag !== 'Online' && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Radio.Group
                  onChange={e => {
                    if (e.target.value === selectedModel?.rename?.data?.name) {
                      return;
                    }
                    const m = renameModels.find(item => item.full_name === e.target.value);
                    if (!m) return;

                    if (flight.status === 'offline' && m.status !== 'installed') {
                      return;
                    }
                    debouncedSwitchOrDownLoadRenameModel('rename', m);
                  }}
                  value={selectedModel?.rename?.data?.name}
                >
                  <div
                    className={classNames(
                      'flex flex-col gap-y-[10px] bg-[#FAFAFA] mt-[16px] p-[12px] border border-[#EBEBEB] rounded-[12px] text-[#333] text-[10px]'
                    )}
                  >
                    <div>{t('selectModelQuality')}</div>
                    <div className="flex gap-x-4">
                      {renameModels.map(item => {
                        return (
                          <Tooltip
                            title={
                              flight.status === 'offline' && item.status !== 'installed'
                                ? t('offlineModeOnlineDisabled')
                                : ''
                            }
                          >
                            <div
                              key={item.labelName}
                              className={classNames(
                                'flex flex-row overflow-hidden justify-between items-center border p-[8px] rounded-[8px] w-[168px] h-[34px] relative',
                                {
                                  'border-[#FFDF75] bg-[#FFF9E5]':
                                    selectedModel?.rename?.data?.name === item.full_name &&
                                    item.status === 'installed', // 已被选择
                                  'border-[#EBEBEB] bg-[#FFFFFF]':
                                    selectedModel?.rename?.data?.name !== item.full_name &&
                                    Object.keys(renameDownloading).includes(item.name), //下载中
                                }
                              )}
                            >
                              {Object.keys(renameDownloading).includes(item.full_name) && (
                                <div
                                  className="top-0 bottom-0 left-0 z-0 absolute bg-gradient-to-r from-[#FFF9E5] to-[#FFDF75] transition-all duration-300"
                                  style={{
                                    width: `${renameDownloading?.[item.full_name] || 0}%`,
                                  }}
                                />
                              )}
                              <Radio
                                disabled={flight.status == 'offline' && item.status !== 'installed'}
                                value={item.full_name}
                              >
                                <div className="z-1 relative text-[12px]">{t(item.labelName)}</div>
                              </Radio>
                              <div className="z-1 relative">
                                {selectedModel?.rename?.data?.name === item.full_name &&
                                  item.status === 'installed' && (
                                    <DeleteIcon
                                      onClick={() => {
                                        if (flightStatus === 'offline') {
                                          message.open({
                                            key: 'modelsWarning',
                                            type: 'warning',
                                            content: t('inPrivacyModeSomeOperations'),
                                          });
                                          return;
                                        }
                                        deleteModel('rename', true, item);
                                      }}
                                      className="w-[14px] h-[14px] text-[#333] cursor-pointer"
                                    />
                                  )}
                                {selectedModel?.rename?.data?.name !== item.full_name &&
                                  item.status !== 'installed' &&
                                  !Object.keys(renameDownloading).includes(item.full_name) && (
                                    <div
                                      className={classNames(
                                        'flex gap-x-1 text-[10px]',
                                        {
                                          ' cursor-not-allowed': flight.status === 'offline',
                                        },
                                        {
                                          ' cursor-pointer': flight.status !== 'offline',
                                        }
                                      )}
                                      onClick={() => {
                                        if (
                                          flight.status === 'offline' &&
                                          item.status !== 'installed'
                                        ) {
                                          return;
                                        }
                                        debouncedSwitchOrDownLoadRenameModel('rename', item);
                                      }}
                                    >
                                      <div>{formatBytesUp(item.size)}</div>
                                      <DownloadIcon className="w-[14px] h-[14px] text-[#FFDF75]" />
                                    </div>
                                  )}
                                {selectedModel?.rename?.data?.name !== item.full_name &&
                                  item.status === 'installed' && (
                                    <DeleteIcon
                                      onClick={() => {
                                        if (flightStatus === 'offline') {
                                          message.open({
                                            key: 'modelsWarning',
                                            type: 'warning',
                                            content: t('inPrivacyModeSomeOperations'),
                                          });
                                          return;
                                        }
                                        deleteModel('rename', false, item);
                                      }}
                                      className="w-[14px] h-[14px] text-[#333] cursor-pointer"
                                    />
                                  )}
                                {Object.keys(renameDownloading).includes(item.full_name) && (
                                  <LoadingIcon className="w-[16px] h-[16px] text-[#FFDF75] rotating" />
                                )}
                              </div>
                            </div>
                          </Tooltip>
                        );
                      })}
                    </div>
                  </div>
                </Radio.Group>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      <div className="flex mt-[30px]">
        <div className="min-w-40 text-[#333] text-[12px]">{t('filesClassification')}</div>
        <div>
          <div>
            {modelTypeColumn.map(item => {
              const isOnlineDisabled = flight.status === 'offline' && item.value === 'Online';
              return (
                <Tooltip
                  key={item.value}
                  title={isOnlineDisabled ? t('offlineModeOnlineDisabled') : ''}
                  placement="top"
                >
                  <Checkbox
                    value={item.value}
                    checked={claTag == item.value}
                    disabled={isOnlineDisabled}
                    onChange={e => onCheckboxChange('classify', e.target.value)}
                  >
                    <div
                      className={classNames(
                        'text-[#333] pl-[12px] w-48 flex items-center h-[34px] rounded-[8px] border text-[12px]',
                        {
                          'border-[#EBEBEB]': claTag !== item.value,
                          'border-[#FFDF75] bg-[#FFF9E5]': claTag === item.value,
                          'opacity-50 cursor-not-allowed': isOnlineDisabled,
                        }
                      )}
                    >
                      {t(item.title)}
                    </div>
                  </Checkbox>
                </Tooltip>
              );
            })}
          </div>
          <AnimatePresence>
            {claTag !== 'Online' && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Radio.Group
                  onChange={e => {
                    if (e.target.value === selectedModel?.embedding?.data?.name) {
                      return;
                    }
                    const m = classifyModels.find(item => item.full_name === e.target.value);
                    if (!m) return;

                    if (flight.status === 'offline' && m.status !== 'installed') {
                      return;
                    }

                    debouncedSwitchOrDownLoadRenameModel('classify', m);
                  }}
                  value={selectedModel?.embedding?.data?.name}
                >
                  <div
                    className={classNames(
                      'flex flex-col gap-y-[10px] bg-[#FAFAFA] mt-[16px] p-[12px] border border-[#EBEBEB] rounded-[12px] text-[#333] text-[10px]'
                    )}
                  >
                    <div>{t('selectModelQuality')}</div>
                    <div className="flex gap-x-4">
                      {classifyModels.map(item => {
                        return (
                          <Tooltip
                            title={
                              flight.status === 'offline' && item.status !== 'installed'
                                ? t('offlineModeOnlineDisabled')
                                : ''
                            }
                          >
                            <div
                              key={item.labelName}
                              className={classNames(
                                'flex flex-row overflow-hidden justify-between items-center border p-[8px] rounded-[8px] w-[168px] h-[34px] relative',
                                {
                                  'border-[#FFDF75] bg-[#FFF9E5]':
                                    selectedModel?.embedding?.data?.name === item.full_name &&
                                    item.status === 'installed', // 已被选择
                                  'border-[#EBEBEB] bg-[#FFFFFF]':
                                    selectedModel?.embedding?.data?.name !== item.full_name &&
                                    Object.keys(classifyDownloading).includes(item.name), //下载中
                                }
                              )}
                            >
                              {Object.keys(classifyDownloading).includes(item.full_name) && (
                                <div
                                  className="top-0 bottom-0 left-0 z-0 absolute bg-gradient-to-r from-[#FFF9E5] to-[#FFDF75] transition-all duration-300"
                                  style={{
                                    width: `${classifyDownloading?.[item.full_name] || 0}%`,
                                  }}
                                />
                              )}
                              <Radio
                                disabled={flight.status == 'offline' && item.status !== 'installed'}
                                value={item.full_name}
                              >
                                <div className="z-1 relative text-[12px]">{t(item.labelName)}</div>
                              </Radio>
                              <div className="z-1 relative">
                                {selectedModel?.embedding?.data?.name === item.full_name &&
                                  item.status === 'installed' && (
                                    <DeleteIcon
                                      onClick={() => {
                                        if (flightStatus === 'offline') {
                                          message.open({
                                            key: 'modelsWarning',
                                            type: 'warning',
                                            content: t('inPrivacyModeSomeOperations'),
                                          });
                                          return;
                                        }
                                        deleteModel('classify', true, item);
                                      }}
                                      className="w-[14px] h-[14px] text-[#333] cursor-pointer"
                                    />
                                  )}
                                {selectedModel?.embedding?.data?.name !== item.full_name &&
                                  item.status !== 'installed' &&
                                  !Object.keys(classifyDownloading).includes(item.full_name) && (
                                    <div
                                      className={classNames(
                                        'flex gap-x-1 text-[10px]',
                                        {
                                          ' cursor-not-allowed': flight.status === 'offline',
                                        },
                                        {
                                          ' cursor-pointer': flight.status !== 'offline',
                                        }
                                      )}
                                      onClick={() => {
                                        if (flight.status === 'offline') {
                                          return;
                                        }
                                        debouncedSwitchOrDownLoadRenameModel('classify', item);
                                      }}
                                    >
                                      <div>{formatBytesUp(item.size)}</div>
                                      <DownloadIcon className="w-[14px] h-[14px] text-[#FFDF75]" />
                                    </div>
                                  )}
                                {selectedModel?.embedding?.data?.name !== item.full_name &&
                                  item.status === 'installed' && (
                                    <DeleteIcon
                                      onClick={() => {
                                        if (flightStatus === 'offline') {
                                          message.open({
                                            key: 'modelsWarning',
                                            type: 'warning',
                                            content: t('inPrivacyModeSomeOperations'),
                                          });
                                          return;
                                        }
                                        deleteModel('classify', false, item);
                                      }}
                                      className="w-[14px] h-[14px] text-[#333] cursor-pointer"
                                    />
                                  )}
                                {Object.keys(classifyDownloading).includes(item.full_name) && (
                                  <LoadingIcon className="w-[16px] h-[16px] text-[#FFDF75] rotating" />
                                )}
                              </div>
                            </div>
                          </Tooltip>
                        );
                      })}
                    </div>
                  </div>
                </Radio.Group>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default ModelDownload;
