import { defaultFields, fields } from '@/utils/fileTemplate';
import classNames from 'classnames';
import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import CloseIcon from '@/assets/svg/close.svg?react';
import DelIcon from '@/assets/svg/delete.svg?react';
import { CUSTOM_FIELD_V2 } from '@/const/localStorage';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Modal } from 'antd';
import { uiEvent } from '@/api/api';

const SortableFieldItem = ({
  item,
  onFieldClick,
}: {
  item: any;
  onFieldClick: (key: string) => void;
}) => {
  const { t } = useTranslation();
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: item.key,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <motion.span
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      layoutId={item.key}
      initial={{ y: 30, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: 30, opacity: 0 }}
      transition={{
        type: 'spring',
        stiffness: 2000,
        damping: 120,
        duration: 0.05,
      }}
      className={classNames(
        'relative flex items-center py-1 justify-start px-5 rounded-lg h-[34px] border text-xs font-semibold cursor-grab active:cursor-grabbing',
        item.color,
        {
          'shadow-lg z-10': isDragging,
        }
      )}
      style={{
        userSelect: 'none',
        ...style,
      }}
    >
      {t(item.key)}
      <CloseIcon
        className={classNames(
          '-top-1 rounded-full -right-1 absolute cursor-pointer z-20',
          item.color
        )}
        onClick={e => {
          e.stopPropagation();
          onFieldClick(item.key);
        }}
        onPointerDown={e => {
          e.stopPropagation();
        }}
        onMouseDown={e => {
          e.stopPropagation();
        }}
      />
    </motion.span>
  );
};

const FileTemplate = (): JSX.Element => {
  const { t } = useTranslation();
  const [preview, setPreview] = useState<string>('');
  const [customField, setCustomField] = useState<any[]>([]);
  const [editMode, setEditMode] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setCustomField(items => {
        const oldIndex = items.findIndex(item => item.key === active.id);
        const newIndex = items.findIndex(item => item.key === over?.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const currentFields = useMemo(() => {
    const cf = localStorage.getItem(CUSTOM_FIELD_V2);
    if (cf) {
      try {
        const parsedFormat = JSON.parse(cf);
        return [...defaultFields, ...parsedFormat];
      } catch (error) {
        return [...defaultFields];
      }
    }
    return [...defaultFields];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshTrigger]);

  useEffect(() => {
    if (customField) {
      const previewText = customField
        .map(item => {
          return item.value;
        })
        .join(' - ');
      setPreview(previewText);
    }
  }, [customField, editMode]);

  const onFieldClick = (key: string) => {
    const field = fields.find(item => item.key === key);
    const tag = customField.find(item => {
      return item.tag === field?.tag;
    });
    if (tag) {
      setCustomField(prev => prev.filter(item => item.tag !== field?.tag));
      return;
    }
    if (!field) {
      return;
    }
    setCustomField(prev => [...prev, { ...field, zhCn: true }]);
  };

  const handleDeleteItem = (_item: any, index: any) => {
    const m = Modal.confirm({
      width: 240,
      title: null,
      footer: null, // 不显示默认的底部按钮
      icon: null, // 不显示默认的图标
      content: (
        <div className="flex flex-col">
          <div>{t('delTemplateTitle')}</div>
          <div className="flex justify-center gap-x-3 mt-5 w-full">
            <div
              onClick={() => {
                m.destroy();
              }}
              className="flex justify-center items-center px-4 border border-[#EBEBEB] rounded-lg h-[34px] text-[#333] text-xs cursor-pointer"
            >
              {t('cancel')}
            </div>
            <div
              onClick={() => {
                const cf = localStorage.getItem(CUSTOM_FIELD_V2);
                if (cf) {
                  try {
                    const parsedFormat = JSON.parse(cf);
                    if (parsedFormat.length > 0 && parsedFormat.length >= index + 1) {
                      const newFormat = parsedFormat.filter((_f: any, idx: any) => idx !== index);
                      localStorage.setItem(CUSTOM_FIELD_V2, JSON.stringify(newFormat));
                    }
                    setRefreshTrigger(prev => prev + 1);
                  } catch (error) {
                    localStorage.setItem(CUSTOM_FIELD_V2, JSON.stringify([]));
                    setRefreshTrigger(prev => prev + 1);
                  }
                } else {
                  localStorage.setItem(CUSTOM_FIELD_V2, JSON.stringify([]));
                  setRefreshTrigger(prev => prev + 1);
                }
                m.destroy();
              }}
              className="flex justify-center items-center bg-[#FFDF75] px-4 border border-[#FFDD6E] rounded-lg h-[34px] text-[#333] text-xs cursor-pointer"
            >
              {t('confirm')}
            </div>
          </div>
        </div>
      ), // 可选：添加确认内容
      centered: true, // 居中显示
    });
  };

  return (
    <div>
      <div className="mt-12 mb-2 font-bold text-sm">{t('chooseNamingFormat')}</div>
      <div className="px-5 border border-[#EBEBEB] rounded-lg">
        {currentFields &&
          currentFields.map((item, index) => {
            return (
              <div
                key={index}
                className={classNames(
                  'flex justify-between items-center  border-[#EBEBEB] border-b last:border-b-0 h-12 text-[#333]'
                )}
              >
                <span className="text-[#333] text-xs">
                  {item
                    .map((field: any) => {
                      return t(field.key);
                    })
                    .join(' - ')}
                </span>
                {index > 2 && (
                  <DelIcon
                    className="w-3 h-3 text-[#999] text-xs cursor-pointer"
                    onClick={() => {
                      handleDeleteItem(item, index - 3);
                    }}
                  />
                )}
              </div>
            );
          })}
      </div>

      <motion.div
        animate={{
          height: editMode ? 'auto' : 'auto',
        }}
        transition={{ duration: 0.4, ease: 'easeInOut' }}
        style={{ overflow: 'hidden' }}
      >
        <AnimatePresence>
          {!editMode && (
            <motion.div
              key="new-button"
              initial={{ height: 28, opacity: 1 }}
              animate={{ height: 28, opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.4, ease: 'easeInOut' }}
              className="flex items-center bg-[#FFF9E5] mt-2 px-[24px] border border-[#FFDF75] rounded-lg w-fit h-[28px] text-[#333] text-xs cursor-pointer"
              style={{ overflow: 'hidden' }}
              onClick={() => {
                uiEvent('Format_new', { Format: 'new' });
                setEditMode(!editMode);
              }}
            >
              {t('newTemplate')}
            </motion.div>
          )}
          {editMode && (
            <motion.div
              key="edit-panel"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.4, ease: 'easeInOut' }}
              className="bg-[#FFF9E5] mt-2 p-4 border border-[#FFDF75] rounded-lg"
              style={{ overflow: 'hidden' }}
            >
              <div className="relative mb-2 w-full">
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={customField.map(item => item.key)}
                    strategy={horizontalListSortingStrategy}
                  >
                    <div className="flex flex-wrap items-center gap-2 bg-white mt-2 px-1 py-1 rounded-lg min-h-[42px] text-gray-800 text-base">
                      <AnimatePresence>
                        {customField.map(item => (
                          <SortableFieldItem
                            key={item.key}
                            item={item}
                            onFieldClick={onFieldClick}
                          />
                        ))}
                      </AnimatePresence>
                    </div>
                  </SortableContext>
                </DndContext>
              </div>
              <div className="mt-4 font-bold text-[#333] text-xs">{t('clickToInsert')}</div>
              <div className="flex flex-wrap items-center gap-2 mt-2 px-1 py-1 min-h-[42px]">
                <AnimatePresence>
                  {fields
                    .filter(field => !customField.some(item => item.key === field.key))
                    .map(field => (
                      <motion.span
                        key={field.key}
                        layoutId={field.key}
                        initial={{ y: 0, opacity: 1 }}
                        animate={{ y: 0, opacity: 1 }}
                        exit={{ y: -30, opacity: 0 }}
                        transition={{
                          type: 'spring',
                          stiffness: 2000,
                          damping: 120,
                          duration: 0.05,
                        }}
                        className={classNames(
                          'flex items-center py-1 cursor-pointer justify-start px-5 rounded-lg h-[34px] border text-xs font-semibold',
                          field.color
                        )}
                        style={{ userSelect: 'none' }}
                        onClick={() => onFieldClick(field.key)}
                      >
                        {t(field.key)}
                      </motion.span>
                    ))}
                </AnimatePresence>
              </div>
              <div className="mt-4 mb-2 font-bold text-[#333] text-xs">{t('formatPreview')}</div>
              <div
                className="flex items-center bg-white px-4 rounded-lg text-[#333] text-xs"
                style={{ minHeight: 48, maxHeight: 80, overflow: 'auto' }}
              >
                {preview || ' '}
              </div>

              <div className="flex justify-end items-center mt-6">
                <div
                  className="flex items-center px-4 text-[#333] text-xs cursor-pointer"
                  onClick={() => {
                    setEditMode(false);
                    setCustomField([]);
                  }}
                >
                  {t('cancel')}
                </div>
                <div
                  className="flex items-center px-4 border border-[#EBEBEB] rounded-lg h-[34px] text-[#333] text-xs cursor-pointer"
                  onClick={() => {
                    setCustomField([]);
                  }}
                >
                  {t('reset')}
                </div>
                <div
                  className={classNames(
                    'flex items-center  ml-4 px-4 rounded-lg h-[34px]  text-xs ',
                    {
                      'cursor-not-allowed bg-[#FFEFB7] text-[#999999]': customField.length === 0,
                      'cursor-pointer bg-[#FFDF75] text-[#333]': customField.length > 0,
                    }
                  )}
                  onClick={() => {
                    if (customField.length === 0) return;
                    const cf = localStorage.getItem(CUSTOM_FIELD_V2);

                    if (cf) {
                      try {
                        const parsedFormat = JSON.parse(cf);
                        localStorage.setItem(
                          CUSTOM_FIELD_V2,
                          JSON.stringify([...parsedFormat, customField])
                        );
                        setRefreshTrigger(prev => prev + 1);
                      } catch (error) {
                        localStorage.setItem(CUSTOM_FIELD_V2, JSON.stringify([customField]));
                        setRefreshTrigger(prev => prev + 1);
                      }
                    } else {
                      localStorage.setItem(CUSTOM_FIELD_V2, JSON.stringify([customField]));
                      setRefreshTrigger(prev => prev + 1);
                    }
                    setEditMode(false);
                    setCustomField([]);
                  }}
                >
                  {t('iWantThis')}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default FileTemplate;
