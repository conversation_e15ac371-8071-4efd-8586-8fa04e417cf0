import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import './styles.css';
import './styles/index.scss';
import './i18n';
import AntdProvider from './providers/AntdProvider';
import { BrowserRouter } from 'react-router-dom';
import { TabsProvider } from '@tauri-apps/plugin-tabs';
import { AliveScope } from 'react-activation';
import { Provider } from 'react-redux';
import { store } from './store/index.ts';

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <Provider store={store}>
    <BrowserRouter>
      <AliveScope>
        <AntdProvider>
          <TabsProvider
            initialTabs={[
              {
                id: 'rename',
                title: 'renameFiles',
                path: 'rename',
              },
            ]}
          >
            <App />
          </TabsProvider>
        </AntdProvider>
      </AliveScope>
    </BrowserRouter>
  </Provider>
);
