.PHONY: submodule-init submodule-update submodule-add submodule-remove

# 初始化所有 submodule
submodule-init:
	git submodule init
	git submodule update

# 更新所有 submodule 到最新版本
submodule-update:
	git submodule update --remote --merge

# 添加新的 submodule
submodule-add:
	git submodule add https://github.com/AtomInnoLab/tauri-apps.git vendor/tauri-apps
	git submodule update --init --recursive

# 移除 submodule
submodule-remove:
	git submodule deinit -f vendor/tauri-apps
	git rm -f vendor/tauri-apps
	git rm --cached -r vendor/tauri-apps
	rm -rf .git/modules/vendor/tauri-apps
