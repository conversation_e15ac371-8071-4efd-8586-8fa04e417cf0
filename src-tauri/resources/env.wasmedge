# Host to listen on
HOST=0.0.0.0
# Port to listen on
PORT=3000
# Maximum upload size in megabytes
MAX_UPLOAD_SIZE_MB=1000
# Sets the API endpoint for the LLM service
LLM_ENDPOINT=http://127.0.0.1:{{WASMEDGE_PORT}}/v1
# llm api key
LLM_API_KEY=
# llm model name.
LLM_MODEL_NAME=QWen2.5-o1
# llm context max tokens
LLM_CONTEXT_MAX_TOKENS=8000
# llm prompt max token
LLM_PROMPT_MAX_TOKEN=7000
# llm max output token
LLM_OUTPUT_MAX_TOKEN=1000
# frequency_penalty
FREQUENCY_PENALTY=1.05
# temperature
TEMPERATURE=0
# Infinity api key
INFINITY_API_KEY=mxrag2024
# Sets the API endpoint for the reranker service
RERANKER_ENDPOINT=http://127.0.0.1:{{WASMEDGE_PORT}}/v1
# Sets the reranker model name
RERANKER_MODEL=BAAI/bge-reranker-v2-m3
# Sets the API endpoint for the embedding service
EMBEDDING_ENDPOINT=http://127.0.0.1:{{WASMEDGE_PORT}}/v1
# Sets the embedding model name
EMBEDDING_MODEL=BAAI/bge-m3
# MAX RERANKER TOKENS
MAX_RERANKER_TOKENS=8192
# API base prefix uri.
BASE_URI=/v1/api
# Reranker threshold
RERANKER_THRESHOLD=0.5
# Embedding threshold
EMBEDDING_THRESHOLD=0.6
# Max file name length
MAX_FILE_NAME_LENGTH=200
# Doc AI Lite url
DOC_AI_URL="http://127.0.0.1:9090/api/doc_lite"
# server mode
SERVER_MODE="Local"
# WISDOC TOOLS
TOOL_BASE_DIR={{APP_DATA}}/wisdoc_tools
# WIS_CONFIG_PATH
WIS_CONFIG_PATH={{APP_DATA}}/.wis_config
# WIS_SERVIER_MANAGER_HOME
WIS_SERVICE_HOME={{APP_DATA}}
# profile sync cache path
PROFILE_CACHE_PATH={{APP_DATA}}
# APP LOG DIR
APP_LOG_DIR={{APP_LOG_DIR}}
# online prefix
ONLINE_URL_PREFIX=http://wisfile-api.dev.atominnolab.com/v1/api
