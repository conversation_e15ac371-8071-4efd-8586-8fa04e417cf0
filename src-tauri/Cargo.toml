[package]
name = "wisfile"
version = "0.1.0"
authors = ["you"]
edition = "2021"
description = "A Tauri App"
license = "MIT OR Apache-2.0"

[lib]
crate-type = ["cdylib", "rlib", "staticlib"]
# 避免与 binary 名冲突（特别是 Windows 平台）
name = "wisfile_lib"

[dependencies]
dotenv = "0.15.0"
duct_sh = "1.0.0"
futures = "0.3.31"
http-body-util = "0.1.0"
log = "0.4"
reqwest = { version = "0.12.15", features = ["json"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
sysinfo = { version = "0.35.1", default-features = false, features = [
  "system",
] }
tauri = { version = "2", features = [ "macos-private-api", "config-json5"] }  # 可选：启用 macOS 特有功能
tauri-plugin-dialog = "2"
tauri-plugin-fs = "2"
tauri-plugin-http = "2"
tauri-plugin-log = "2"
tauri-plugin-opener = "2"
tauri-plugin-os = "2"
tauri-plugin-shell = "2"
tauri-plugin-store = "2"
tempfile = "3.20.0"
tokio = { version = "1.44.1", features = ["full"] }
fs_extra = "1.3"
tower-service = "0.3.3"
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.18", features = ["env-filter", "fmt"] }
util = { path = "../util" }
walkdir = "2.5.0"
uuid = "1.17.0"

# [target.'cfg(not(target_os = "windows"))'.dependencies]
service-manager = { git = "ssh://**************/AtomInnoLab/ServiceManager.git", branch = "dev", version = "*" }
wisfile-backend = { git = "ssh://**************/AtomInnoLab/WisFile-Backend.git", branch = "feat/custom-mupdf", version = "*", package = "server" }
conf = { git = "ssh://**************/AtomInnoLab/WisFile-Backend.git", branch = "feat/custom-mupdf", version = "*", package = "conf" }
wisdoc_emb = { git = "ssh://**************/AtomInnoLab/WisFile-Backend.git", branch = "feat/custom-mupdf", version = "*", package = "wisdoc_emb" }
# service-manager = { path = "../../service-manager", version = "*" }
# wisfile-backend = { path = "../../WisFile-Backend/crates/server", version = "*", package = "server" }
# conf = { path = "../../WisFile-Backend/crates/conf", version = "*", package = "conf" }
# wisdoc_emb = { path = "../../WisFile-Backend/crates/wisdoc_emb", version = "*", package = "wisdoc_emb" }

[build-dependencies]
tauri-build = { version = "2.0.0", features = ["config-json5"] }
