{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "fs:default", "dialog:default", "os:default", "store:default", "http:default", "core:window:default", "fs:allow-stat", "log:default", "shell:allow-open", "core:window:allow-toggle-maximize", "core:window:allow-internal-toggle-maximize", "core:window:allow-minimize", "core:window:allow-close", "core:window:allow-hide", "core:window:allow-show", "core:window:allow-maximize", "core:window:allow-unmaximize", "core:window:allow-unminimize", "core:window:allow-start-dragging", "core:window:allow-set-fullscreen", "core:window:deny-is-always-on-top", "core:window:allow-set-decorations", {"identifier": "fs:scope", "allow": [{"path": "$HOME"}, {"path": "$HOME/**"}]}]}