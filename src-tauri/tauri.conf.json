{"$schema": "https://schema.tauri.app/config/2", "productName": "WisFile", "version": "1.2.20", "identifier": "com.yyzj.wisfile", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"macOSPrivateApi": true, "windows": [{"title": "", "width": 1100, "height": 700, "minWidth": 800, "minHeight": 700, "fullscreen": false, "resizable": true, "center": true, "dragDropEnabled": true, "decorations": false, "hiddenTitle": true, "transparent": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "macOS": {"minimumSystemVersion": "10.13", "signingIdentity": "Developer ID Application: <PERSON><PERSON> (WQXWZGR6QW)", "dmg": {}}, "resources": {"resources/env": "resources/env", "resources/llamacpp": "libs/"}, "externalBin": ["resources/llama-server"], "linux": {"deb": {"depends": ["tesseract-ocr", "libssl-dev"]}, "rpm": {"depends": ["tesseract-ocr", "libssl-dev"]}}}, "plugins": {"dialog": null, "fs": null, "os": null, "store": null, "http": null, "shell": null}}