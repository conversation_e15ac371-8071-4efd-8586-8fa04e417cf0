use std::sync::atomic::Ordering;

use serde::{Deserialize, Serialize};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use tauri_plugin_store::StoreExt;
use tokio::sync::Mutex;
use util::{
    device::{init_app_version, init_device_sn},
    telemetry::{
        event::{init_channel, init_group, TelemetrySend, WisFileEvent},
        FLY_MODE,
    },
};

const APP_CONFIG_KEY: &str = "app_config";
const APP_CONFIG_STORE: &str = "config.json";

pub struct WisConfig(pub Mutex<WisConfigInner>);

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WisConfigInner {
    pub fly_mode: bool,
    pub user_id: String,
    pub group: Option<String>,
    pub channel: Option<String>,
    pub app_version: String,
}

impl Default for WisConfigInner {
    fn default() -> Self {
        Self {
            fly_mode: false,
            user_id: uuid::Uuid::new_v4().to_string(),
            app_version: String::new(),
            group: None,
            channel: None,
        }
    }
}

impl WisConfig {
    pub fn init(app: &AppHandle) {
        log::info!("init app config");
        let config = WisConfigInner::load(app);
        log::info!("add app config to tauri");
        app.manage(WisConfig(Mutex::new(config)));
    }
}

impl WisConfigInner {
    pub fn load(app: &AppHandle) -> Self {
        fn load_inner(app: &AppHandle) -> Result<WisConfigInner, tauri_plugin_store::Error> {
            let store = app.store(APP_CONFIG_STORE)?;

            let selected_models = store.get(APP_CONFIG_KEY);
            let app_config = selected_models
                .and_then(|value| serde_json::from_value::<WisConfigInner>(value).ok())
                .unwrap_or(WisConfigInner::default());

            log::info!("load app config {app_config:?}");
            Ok(app_config)
        }

        let mut config = match load_inner(app) {
            Ok(config) => config,
            Err(err) => {
                log::error!("load app config error, {err}");
                WisConfigInner::default()
            }
        };

        let version = app.config().version.as_deref().unwrap_or("0.0.0");

        // app version is empty or user id is empty or app version is not equal to current version
        if config.app_version.is_empty()
            // || config.app_version != version
            || config.user_id.is_empty()
        {
            let (user_id, group, channel) = tauri::async_runtime::block_on(async move {
                let event = WisFileEvent::FirstOpen;
                let uuid = uuid::Uuid::new_v4().to_string();
                if let Ok(response) = event.send(&uuid).await {
                    let user_id = response
                        .get("data")
                        .and_then(|value| value.get("user_id"))
                        .and_then(|user_id| user_id.as_str());

                    let group = response
                        .get("data")
                        .and_then(|value| value.get("download_enevt"))
                        .and_then(|value| value.get("group"))
                        .and_then(|group| group.as_str())
                        .map(|group| group.to_string());

                    let channel = response
                        .get("data")
                        .and_then(|value| value.get("download_enevt"))
                        .and_then(|value| value.get("channel"))
                        .and_then(|channel| channel.as_str())
                        .map(|channel| channel.to_string());

                    if let Some(user_id) = user_id {
                        (user_id.to_string(), group, channel)
                    } else {
                        (uuid, group, channel)
                    }
                } else {
                    (uuid, None, None)
                }
            });

            config.group = group;
            config.channel = channel;
            config.app_version = version.to_string();
            config.user_id = user_id;
        }

        log::info!("load wis config {config:?}");

        log::info!("init device sn with {}", config.user_id);
        init_device_sn(config.user_id.clone());
        log::info!("init channel with {:?}", config.channel);
        init_channel(config.channel.clone());
        log::info!("init group with {:?}", config.group);
        init_group(config.group.clone());

        FLY_MODE.store(config.fly_mode, Ordering::SeqCst);
        config
    }

    pub async fn update_config<F: FnOnce(&mut WisConfigInner)>(
        app: &AppHandle,
        f: F,
    ) -> Result<(), tauri_plugin_store::Error> {
        let Some(config) = app.try_state::<WisConfig>() else {
            log::info!("app config not init yet");
            return Ok(());
        };

        let mut config = config.0.lock().await;
        f(&mut config);
        config.save(app)?;

        Ok(())
    }

    pub fn save(&self, app: &AppHandle) -> Result<(), tauri_plugin_store::Error> {
        log::info!("store app config {self:?}");

        let value = serde_json::to_value(self).unwrap();
        let store = app.store(APP_CONFIG_STORE)?;
        store.set(APP_CONFIG_KEY, value);
        log::info!("store app config success");

        Ok(())
    }
}

pub fn set_app_version(app: &AppHandle) {
    let version = app.config().version.as_deref().unwrap_or("0.0.0");

    log::info!("init app version {version}");
    init_app_version(version);
}
