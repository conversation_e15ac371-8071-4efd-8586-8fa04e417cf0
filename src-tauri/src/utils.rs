use fs_extra;
use std::{
    fs::{self, create_dir_all, OpenOptions},
    io::Write,
    ops::Deref,
    path::{Path, PathBuf},
    sync::LazyLock,
};

use conf::CONFIG;
use tauri::{App, Manager as _};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, Layer};
use util::{
    path::{app_data_dir, app_log_dir},
    port::find_available_port,
};

// llama embedding port
pub static LLAMA_EMBEDDING_PORT: LazyLock<u16> =
    LazyLock::new(|| find_available_port(20500).unwrap_or(20505));
// llama rename port
pub static LLAMA_RENAME_PORT: LazyLock<u16> =
    LazyLock::new(|| find_available_port(20800).unwrap_or(20805));

static ONLINE_URL_PREFIX: LazyLock<String> = LazyLock::new(|| {
    std::env::var("ONLINE_URL_PREFIX")
        .unwrap_or("http://wisfile-api.dev.atominnolab.com/v1/api".to_string())
});

const ILLEGAL_CHARS: &[&str] = &["/", "\\", ":", "*", "?", "\"", "\n"];

pub fn online_url(api: &str) -> String {
    format!("{}{}", ONLINE_URL_PREFIX.as_str(), api)
}

#[inline(always)]
pub fn home_dir() -> PathBuf {
    #[allow(deprecated)]
    match std::env::home_dir() {
        Some(home) => home,
        None => {
            panic!("Can not get HOME dir, please check")
        }
    }
}

pub fn inti_env(app: &App) {
    let app_data_path = app_data_dir(app, &[]);

    if !app_data_path.exists() {
        log::info!("create app data dir {}", app_data_path.display());
        let _ = create_dir_all(&app_data_path);
    }

    {
        let env = app
            .path()
            .resource_dir()
            .map(|path| path.join("resources").join("env"));

        match env {
            Ok(path) => {
                if !path.exists() {
                    let _ = dotenv::dotenv().ok();
                } else {
                    let env_template = fs::read_to_string(path)
                        .map_err(|err| log::error!("read env template error, {err}"))
                        .unwrap();

                    // fix blank path
                    let _app_data_path = if cfg!(unix) {
                        // on unix, we can use the path directly
                        app_data_path.to_string_lossy().replace(" ", "\\ ")
                    } else {
                        // on windows, we need to escape the space in path
                        format!(
                            "{}\\\\",
                            app_data_path
                                .to_string_lossy()
                                .replace("\\", "\\\\")
                                .replace(" ", "\\ "),
                        )
                    };

                    let mut config = env_template.replace("{{APP_DATA}}", &_app_data_path);

                    config = config.replace(
                        "{{LLAMA_EMBEDDING_PORT}}",
                        LLAMA_EMBEDDING_PORT.to_string().as_str(),
                    );
                    config = config.replace(
                        "{{LLAMA_RENAME_PORT}}",
                        LLAMA_RENAME_PORT.to_string().as_str(),
                    );

                    let app_log_dir = app_log_dir(app);
                    // fix blank path
                    let _app_log_dir = if cfg!(unix) {
                        // on unix, we can use the path directly
                        app_log_dir.to_string_lossy().replace(" ", "\\ ")
                    } else {
                        // on windows, we need to escape the space in path
                        format!(
                            "{}\\\\",
                            app_log_dir
                                .to_string_lossy()
                                .replace("\\", "\\\\")
                                .replace(" ", "\\ "),
                        )
                    };

                    config = config.replace("{{APP_LOG_DIR}}", &_app_log_dir);

                    let real_env = app_data_path.join("env");

                    let mut env = OpenOptions::new()
                        .create(true)
                        .truncate(true)
                        .write(true)
                        .open(&real_env)
                        .map_err(|err| log::error!("open {} failed, {}", real_env.display(), err))
                        .unwrap();
                    env.write_all(config.as_bytes())
                        .map_err(|err| {
                            log::error!("write config to {} failed. {}", real_env.display(), err)
                        })
                        .unwrap();

                    log::info!("load .env file from {}", real_env.display());
                    let result = dotenv::from_filename(real_env);
                    println!("dotenv result: {result:?}");
                }
            }
            Err(err) => {
                log::error!("Can not get the env file, just fallback to .env. {err}");
                let _ = dotenv::dotenv().ok();
            }
        }
    }

    // Initialize configuration
    let _ = CONFIG.deref();
}

pub fn init_log(app: &App, terminal: bool) {
    let log_path = app_log_dir(app);

    let file_appender = tracing_appender::rolling::daily(log_path, "wisfile.log");

    let env_filter =
        "info,lopdf::document=error,lopdf::object=error,parse-doc=error,lopdf::parser_aux=error";
    let filter = tracing_subscriber::EnvFilter::new(env_filter);
    let rolling_output = tracing_subscriber::fmt::layer()
        .with_writer(file_appender)
        .with_ansi(false)
        .with_target(true)
        .with_level(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        // .with_file(true)
        // .with_line_number(true)
        .compact()
        .with_filter(filter);

    let filter = tracing_subscriber::EnvFilter::new(env_filter);
    let console_output = tracing_subscriber::fmt::layer()
        .with_writer(std::io::stdout)
        .with_ansi(true)
        .with_target(true)
        .with_level(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        // .with_file(true)
        // .with_line_number(true)
        .compact()
        .with_filter(filter);

    if terminal {
        tracing_subscriber::registry()
            .with(rolling_output)
            .with(console_output)
            .init();
    } else {
        tracing_subscriber::registry().with(rolling_output).init();
    }
}

pub fn fs_rename<P: AsRef<Path>>(src: P, new_name: &str) -> Result<PathBuf, String> {
    let path = src.as_ref();
    log::info!(
        "file rename src: {}, new_name: {}",
        path.display(),
        new_name
    );

    log::info!("will rename the file");
    let old_ext = path
        .extension()
        .map(|ext| ext.to_string_lossy().to_string())
        .unwrap_or("pdf".to_string());

    let parent = path.parent().unwrap();
    let mut target_path = parent.join(new_name);
    // if new target path has a same extension, then don't add extension
    if target_path
        .extension()
        .is_none_or(|ext| ext != old_ext.as_str())
    {
        target_path.add_extension(old_ext);
    }

    // if target_path.exists() {
    //     return Ok(target_path);
    // }

    if target_path == path {
        return Ok(target_path);
    }

    log::info!("mv {} -> {}", path.display(), target_path.display());
    fs::rename(path, &target_path).map_err(|err| {
        log::error!("rename file error. {err}");
        err.to_string()
    })?;

    Ok(target_path)
}

pub fn fs_mv_file<P: AsRef<Path>, F: AsRef<Path>>(src: P, target: F) -> Result<PathBuf, String> {
    let src_path = src.as_ref();

    debug_assert!(src_path.is_file() && src_path.is_absolute());
    let target_path = target.as_ref();
    if !target_path.exists() {
        log::info!(
            "target dir {} not exits, create first",
            target_path.display()
        );
        let _ = fs::create_dir_all(target_path);
    }
    debug_assert!(target_path.is_dir() && target_path.is_absolute());

    let file_name = src_path.file_name().unwrap();
    let target_path = target_path.join(file_name);
    log::info!("mv {} to {}", src_path.display(), target_path.display());

    // if target_path.exists() {
    //     return Err("File Name Duplicate".to_string());
    // }

    if target_path == src_path {
        return Ok(target_path);
    }

    let options = fs_extra::file::CopyOptions::new();
    fs_extra::file::move_file(src_path, &target_path, &options).map_err(|err| {
        log::error!("mv file error. {err}");
        err.to_string()
    })?;

    Ok(target_path)
}

pub fn sanitize_filename(name: &str) -> String {
    let mut s = String::from(name);
    for c in ILLEGAL_CHARS {
        s = s.replace(c, " ");
    }
    s
}
