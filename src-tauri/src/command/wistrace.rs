use std::{
    collections::HashMap,
    sync::{Arc, LazyLock, Mutex},
};

use std::io::Write;

use tauri::Manager;
use tempfile::Builder;
use util::{
    path::app_data_dir_inner,
    telemetry::wistrace::{
        update_collection_email, wistrace_upload_file, IssueKind, TracingCollection,
    },
};

use crate::state::AppState;

pub static TRACING_INFO: LazyLock<Arc<Mutex<HashMap<String, String>>>> =
    LazyLock::new(|| Arc::new(Mutex::new(HashMap::new())));

#[tauri::command]
pub async fn create_collection(
    issue_kind: IssueKind,
    app_handle: tauri::AppHandle,
) -> Result<String, String> {
    log::info!("create collection for {issue_kind:?}");
    let app_version = app_handle
        .config()
        .version
        .clone()
        .unwrap_or("unknown".to_string());
    let collection = TracingCollection::new(issue_kind, &app_version);

    collection
        .create_collection()
        .await
        .map_err(|err| err.to_string())
}

#[tauri::command]
pub async fn upload_file(
    source_path: String,
    collection: String,
    issue_kind: IssueKind,
    email: Option<String>,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    log::info!(
        "upload {source_path} to collection {collection}, kind: {issue_kind:?}, email {email:?}"
    );
    let app_data_path = app_data_dir_inner(&app_handle, &[]);

    if issue_kind.contains_classify() {
        let app_state = app_handle.state::<AppState>();
        log::info!("save embedding");
        app_state.wisfile.1.save_profile().await.ok();

        log::info!("upload embedding.bin");
        wistrace_upload_file(app_data_path.join("embedding.bin"), &collection)
            .await
            .map_err(|err| err.to_string())?;
    }

    if issue_kind.contains_rename() {
        let debug_info = {
            let tracing_info = TRACING_INFO.lock().unwrap();
            tracing_info.get(&source_path).cloned()
        };
        if let Some(debug_info) = debug_info {
            let mut tmp_file = Builder::new()
                .prefix("tracing_")
                .tempfile_in(&app_data_path)
                .map_err(|err| err.to_string())?;

            writeln!(tmp_file, "{debug_info}").map_err(|err| err.to_string())?;

            tmp_file.flush().ok();

            let path = tmp_file.into_temp_path();
            wistrace_upload_file(path.to_path_buf(), &collection)
                .await
                .map_err(|err| err.to_string())?;
        }
    }
    log::info!("upload embedding or rename log done");

    wistrace_upload_file(source_path, &collection)
        .await
        .map_err(|err| err.to_string())?;
    log::info!("upload src file done");

    // update email
    if let Some(email) = email {
        if !email.is_empty() {
            log::info!("try to update {collection} email to {email}");
            update_collection_email(&email, &collection)
                .await
                .map_err(|err| err.to_string())?;
        }
    }

    Ok(())
}
