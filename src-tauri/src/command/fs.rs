use crate::utils::{fs_mv_file, fs_rename, sanitize_filename};

#[tauri::command]
pub async fn file_rename(src: String, new_name: String) -> Result<String, String> {
    let new_name = sanitize_filename(&new_name);
    log::info!("file rename src: {src}, new_name: {new_name}");
    fs_rename(src, &new_name).map(|path| path.to_string_lossy().to_string())
}

#[tauri::command]
pub async fn mv_file(src: String, target: String) -> Result<String, String> {
    log::info!("mv file {src} to {target}");
    fs_mv_file(&src, &target).map(|path| path.to_string_lossy().to_string())
}
