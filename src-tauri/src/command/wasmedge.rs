use std::collections::HashMap;

use http_body_util::BodyExt as _;
use service_manager::{
    api::service::ServiceRequest, service::ServiceType, wis_archive::models::model_file::ModelDesc,
};
use tower_service::Service;
use wisfile_backend::axum::Router;

use crate::{
    command::{build_axum_request, HttpRequest},
    state::{AppState, ModelState},
    utils::get_wasmedge_port,
    wasmedge::WasmEdgePath,
};

#[tauri::command]
pub async fn wasmedge_path(state: tauri::State<'_, WasmEdgePath>) -> Result<String, ()> {
    Ok(state.0.lock().await.to_string())
}

#[tauri::command]
pub fn wasmedge_port() -> Result<u16, String> {
    Ok(get_wasmedge_port())
}

pub async fn call_service_manager<T: serde::Serialize>(
    body: &T,
    path: &str,
    method: &str,
    routers: &mut Router,
) -> Result<String, String> {
    let mut headers = HashMap::new();
    headers.insert("Content-Type".to_string(), "application/json".to_string());
    let request = HttpRequest {
        path: path.to_string(),
        method: method.to_string(),
        body: Some(serde_json::to_string(body).map_err(|err| {
            log::error!("serialize request error. {err}");
            err.to_string()
        })?),
        headers,
    };
    let request = build_axum_request(request)?;
    let response = {
        routers.call(request).await.map_err(|err| {
            log::error!("call service manager error. {err}");
            err.to_string()
        })?
    };
    if !response.status().is_success() {
        let body = response
            .into_body()
            .collect()
            .await
            .unwrap()
            .to_bytes()
            .to_vec();
        let error = String::from_utf8(body).unwrap();
        log::error!("stop service error. {error}");
        return Err(error);
    }

    let body = response
        .into_body()
        .collect()
        .await
        .unwrap()
        .to_bytes()
        .to_vec();

    Ok(String::from_utf8(body).unwrap())
}

#[tauri::command]
pub async fn start_wasmedge(
    models: Vec<ModelDesc>,
    app: tauri::State<'_, AppState>,
    wasmedge_path: tauri::State<'_, WasmEdgePath>,
) -> Result<String, String> {
    let wasmedge_path = { wasmedge_path.0.lock().await.to_string() };

    let mut request_body = ServiceRequest {
        service_type: ServiceType::PackedWasmEdge {
            path: wasmedge_path.clone(),
        },
        models: None,
    };

    let body = {
        log::info!("stop the wasmedge first");
        let mut app = app.service_manager.0.lock().await;
        call_service_manager(&request_body, "/api/v1/service/stop", "POST", &mut app).await?;

        if !models.is_empty() {
            log::info!("start the wasmedge");
            request_body.models = Some(models);
            call_service_manager(&request_body, "/api/v1/service/start", "POST", &mut app).await?
        } else {
            log::info!("models is empty just skip");
            String::from("stop wasmedge successfully")
        }
    };

    Ok(body)
}

pub async fn _start_wasmedge(
    app: tauri::State<'_, AppState>,
    current_model: tauri::State<'_, ModelState>,
    wasmedge_path: tauri::State<'_, WasmEdgePath>,
) -> Result<(), String> {
    let current_model = { current_model.0.lock().await.clone() };
    log::info!("start wasmedge at start");

    let models = current_model
        .models()
        .into_iter()
        .map(|model| ModelDesc {
            name: model.name,
            r#type: model.r#type,
        })
        .collect();

    log::info!("start wasmedge at start, models {models:?}");

    let _ = start_wasmedge(models, app, wasmedge_path).await?;

    Ok(())
}
