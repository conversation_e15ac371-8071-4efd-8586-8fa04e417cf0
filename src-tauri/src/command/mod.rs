use std::collections::HashMap;
use util::log::archive_log;
use wisfile_backend::axum::{body::Body, extract::Request};

pub mod fs;
pub mod llama;
pub mod locale;
pub mod model;
pub mod services;
pub mod ui_event;

pub mod wisconfig;
pub mod wisfile;
pub mod wistrace;

#[derive(serde::Serialize, serde::Deserialize, Debug)]
pub struct HttpRequest<T> {
    path: String,
    method: String,
    body: Option<T>,
    headers: HashMap<String, String>,
}

pub fn build_axum_request(request: HttpRequest<String>) -> Result<Request<Body>, String> {
    let body = match request.body {
        Some(body) => Body::from(body.into_bytes()),
        None => Body::empty(),
    };

    let mut builder = Request::builder();

    for (key, value) in request.headers {
        builder = builder.header(key, value);
    }

    builder
        .method(request.method.as_str())
        .uri(request.path)
        .body(body)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn export_logs(app_handle: tauri::AppHandle) -> Result<String, String> {
    log::info!("start export log");
    archive_log(&app_handle).map(|p| p.to_string_lossy().to_string())
}

pub mod prelude {
    pub use crate::command::fs::*;
    pub use crate::command::llama::*;
    pub use crate::command::model::*;
    pub use crate::command::services::*;
    pub use crate::command::ui_event::*;

    pub use crate::command::wisconfig::*;
    pub use crate::command::wisfile::*;
    pub use crate::command::wistrace::*;
    pub use crate::command::*;
}
