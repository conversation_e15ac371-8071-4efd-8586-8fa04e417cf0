use std::sync::atomic::Ordering;

use tauri::Manager as _;

use crate::{
    llama::start_llama,
    state::{AppState, ModelState, SelectedModel},
};

#[tauri::command]
pub async fn set_model(
    model_type: SelectedModel,
    force_restart: bool,
    app_handle: tauri::AppHandle,
) -> Result<(), String> {
    log::info!("set model to {model_type:?}, force_restart {force_restart}");

    // set wisfile use_local_model
    if let Some(app_state) = app_handle.try_state::<AppState>() {
        log::info!(
            "set wisfile use local mode to {}",
            !model_type.is_embedding_online()
        );
        app_state
            .wisfile
            .1
            .set_local_model(!model_type.is_embedding_online());
        log::info!(
            "now wisfile use_local_model is {}",
            app_state
                .wisfile
                .1
                .use_local_embedding
                .load(Ordering::SeqCst)
        );
        log::info!(
            "now wisfile use_local_model is {}",
            app_state
                .wisfile
                .1
                .profile_sync
                .as_ref()
                .unwrap()
                .queue
                .use_local_embedding
                .load(Ordering::SeqCst)
        );
        log::info!(
            "now wisfile use_local_model is {}",
            app_state
                .wisfile
                .1
                .profile_sync
                .as_ref()
                .unwrap()
                .cache
                .use_local_embedding
                .load(Ordering::SeqCst)
        );
    };

    let current_model = app_handle.state::<ModelState>();
    let _current_model = { current_model.0.lock().await.clone() };

    // 模型未发生改变,且没有强制重启
    if model_type == _current_model && !force_restart {
        log::info!("model not changed, just skip");
        return Ok(());
    }

    *current_model.0.lock().await = model_type.clone();

    let _ = start_llama(&app_handle, model_type).await;

    Ok(())
}

#[tauri::command]
pub async fn get_model(
    current_model: tauri::State<'_, ModelState>,
) -> Result<SelectedModel, String> {
    let current_model = { current_model.0.lock().await.clone() };
    log::info!("get model, current models {current_model:?}");

    Ok(current_model)
}
