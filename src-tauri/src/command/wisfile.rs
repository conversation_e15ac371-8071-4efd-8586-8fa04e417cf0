use std::{
    fs,
    path::{Path, PathBuf},
    time::Instant,
};

use futures::StreamExt;
use http_body_util::BodyExt as _;
use serde::{Deserialize, Serialize};
use tauri::ipc::Channel;
use tower_service::Service as _;
use util::{
    device::device_sn,
    telemetry::event::{TelemetrySend, WisFileEvent},
};
use wisdoc_emb::prelude::parse_document_from_path;
use wisfile_backend::{
    profile::sync::ClassifyItem,
    routers::file::{
        rename::generate_filename,
        types::{RenameFileRequest, RenameFileResponse},
    },
    state::AppState as WisFileState,
};

use crate::{
    command::wistrace::TRACING_INFO,
    state::{AppState, ModelState},
    utils::sanitize_filename,
};

use super::{build_axum_request, HttpRequest};

#[tauri::command]
pub async fn wisfile(
    request: HttpRequest<String>,
    app: tauri::State<'_, AppState>,
) -> Result<String, String> {
    let request = build_axum_request(request).map_err(|err| {
        log::error!("build service request error. {err}");
        err
    })?;

    let response = {
        let mut app = app.wisfile.0.lock().await;
        app.call(request).await.map_err(|err| {
            log::error!("call service manager error. {err}");
            err.to_string()
        })?
    };

    if !response.status().is_success() {
        let body = response
            .into_body()
            .collect()
            .await
            .unwrap()
            .to_bytes()
            .to_vec();
        return Err(String::from_utf8(body).unwrap());
    }

    let body = response
        .into_body()
        .collect()
        .await
        .unwrap()
        .to_bytes()
        .to_vec();

    Ok(String::from_utf8(body).unwrap())
}

#[derive(Clone, Serialize)]
#[serde(
    rename_all = "camelCase",
    rename_all_fields = "camelCase",
    tag = "event",
    content = "data"
)]
pub enum RenameClassify {
    Rename { src: String, target: String },
    RenameError { src: String, err_msg: String },
    Classify { src: String, target: String },
    ClassifyError { src: String, err_msg: String },
    ClassifyProcess { src: usize, total: usize },
}
// 公司名-时间-版本号-会议名-作者-标题

#[derive(Clone, Copy, Default, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum RenameTags {
    CompanyName,
    Date,
    Version,
    ConferenceName,
    Author,
    #[default]
    Title,
    ShortName,
}

#[derive(Clone, Copy, Default, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RenameTemplate {
    pub tag: RenameTags,
    pub zh_cn: bool,
}

impl RenameTemplate {
    pub fn solt_name(&self) -> &str {
        if self.zh_cn {
            self.tag.slot_cn_name()
        } else {
            self.tag.slot_en_name()
        }
    }
}

impl RenameTags {
    pub fn slot_cn_name(&self) -> &str {
        match self {
            RenameTags::CompanyName => "公司名",
            RenameTags::Date => "时间",
            RenameTags::Version => "版本号",
            RenameTags::ConferenceName => "会议名",
            RenameTags::Author => "作者",
            RenameTags::Title => "文章标题",
            RenameTags::ShortName => "文章简称",
        }
    }

    pub fn slot_en_name(&self) -> &str {
        match self {
            RenameTags::CompanyName => "Company Name",
            RenameTags::Date => "Time",
            RenameTags::Version => "Version Number",
            RenameTags::ConferenceName => "Conference Name",
            RenameTags::Author => "Author",
            RenameTags::Title => "Article Title",
            RenameTags::ShortName => "Article Short Title",
        }
    }
}

// rename and classify
#[tauri::command]
#[allow(clippy::too_many_arguments)]
pub async fn rename_and_classify(
    source: Vec<String>,
    target: Option<String>,
    only_classify: bool,
    rename_tags: Vec<RenameTemplate>,
    on_event: Channel<RenameClassify>,
    app: tauri::State<'_, AppState>,
    current_model: tauri::State<'_, ModelState>,
) -> Result<(), String> {
    if !only_classify {
        let current_model = { current_model.0.lock().await.clone() };
        let is_rename_online = current_model.is_rename_online();

        log::info!("start rename task for {source:?}");
        handle_rename(&source, rename_tags, is_rename_online, &on_event).await?;
    }

    if let Some(target) = target {
        log::info!("start to classify file to {target}");
        file_classify(&app.wisfile.1, source.clone(), target, &on_event).await?;
    }

    Ok(())
}

#[tauri::command]
pub async fn embedding_dirs(
    target: &str,
    on_event: Channel<RenameClassify>,
    app: tauri::State<'_, AppState>,
) -> Result<(), String> {
    let wisfile = &app.wisfile.1.clone();
    let stream = wisfile
        .embedding_dirs(target)
        .await
        .map_err(|err| err.to_string())?;

    let mut stream = Box::pin(stream);
    while let Some(classify) = stream.next().await {
        log::info!("rec dir embedding {classify:?}");
        send_classify_data(classify, &on_event);
    }
    Ok(())
}

// extra a file content
fn extra_file<P: AsRef<Path>>(path: P) -> Result<RenameFileRequest, String> {
    let path = path.as_ref();
    log::info!("parse pdf {}", path.display());

    let file_name = path
        .file_name()
        .map(|name| name.to_string_lossy().to_string())
        .ok_or_else(|| format!("path {} not contains a file name", path.display()))?;

    let now = Instant::now();
    let content = parse_document_from_path(path)
        .map_err(|err| {
            log::error!("parse document error. {err}");
            err.to_string()
        })
        .map(|content| content.text)?;

    log::info!(
        "parse document {}, const {}",
        path.display(),
        now.elapsed().as_micros()
    );

    Ok(RenameFileRequest {
        old_name: file_name,
        content: Some(content),
    })
}

/// rename file task
async fn handle_rename(
    source: &[String],
    rename_tags: Vec<RenameTemplate>,
    is_rename_online: bool,
    on_event: &Channel<RenameClassify>,
) -> Result<(), String> {
    let rename_template = if rename_tags.is_empty() {
        "命名模式：文章标题\n文本：".to_string()
    } else {
        let tags = rename_tags
            .iter()
            .map(|tag| tag.solt_name())
            .collect::<Vec<_>>()
            .join("-");
        log::info!("rename will apply the template {tags}");
        format!("命名模式：{tags}\n文本：")
    };

    let rename_tasks = source
        .iter()
        .map(|source| rename(source, is_rename_online, &rename_template))
        .collect::<Vec<_>>();

    let mut stream = futures::stream::iter(rename_tasks).buffer_unordered(10);

    while let Some(rename) = stream.next().await {
        match rename {
            Ok((src, resp)) => {
                let new_name = sanitize_filename(&resp.new_name);
                log::info!("sanitize_filename from {} to {}", resp.new_name, new_name);

                log::info!("rename {src} successfully");
                on_event
                    .send(RenameClassify::Rename {
                        src,
                        target: new_name,
                    })
                    .unwrap()
            }
            Err((src, err_msg)) => {
                log::error!("rename {src} failed. {err_msg}");
                on_event
                    .send(RenameClassify::RenameError { src, err_msg })
                    .unwrap()
            }
        }
    }

    let file_count = source.len();
    tauri::async_runtime::spawn(async move {
        // send file_rename event
        log::info!("start send file rename event count: {file_count}");
        let event = WisFileEvent::FileRename { count: file_count };
        if let Err(err) = event.send(device_sn()).await {
            log::error!("Failed to send app open event: {err}");
        };
    });

    Ok(())
}

/// rename a file
async fn rename<P: AsRef<Path>>(
    path: P,
    online: bool,
    rename_template: &str,
) -> Result<(String, RenameFileResponse), (String, String)> {
    let path = path.as_ref();
    let src_str = path.to_string_lossy().to_string();
    log::info!("rename {} online {}", path.display(), online,);

    if !path.is_file() || !path.is_absolute() {
        return Err((
            src_str,
            format!("path {} is not a absolute file path", path.display()),
        ));
    }

    let RenameFileRequest { old_name, content } = extra_file(path).map_err(|err| {
        log::error!("parse document {} content error. {}", path.display(), err);
        (src_str.clone(), err)
    })?;

    let content = content.unwrap();
    let request = RenameFileRequest {
        old_name,
        content: Some(format!("{rename_template}{content}")),
    };

    let resp = generate_filename(&request, !online)
        .await
        .map(|(new_name, debug_info)| RenameFileResponse {
            old_name: request.old_name,
            new_name,
            debug_info,
        })
        .map_err(|err| (src_str.clone(), err.error.to_string()))?;

    log::info!("rename resp: {resp:?}");
    // save tracing info
    let mut tracing_info = TRACING_INFO.lock().unwrap();
    tracing_info.insert(path.to_string_lossy().to_string(), resp.debug_info.clone());

    if resp.new_name == resp.old_name {
        log::info!("llm generate new_name same as old_name");
        return Err((src_str, "Can Not Generate A New Name".to_string()));
    }

    Ok((path.to_string_lossy().to_string(), resp))
}

/// classify file
async fn file_classify<P: AsRef<Path>>(
    wisfile: &WisFileState,
    src: Vec<String>,
    target: P,
    channel: &Channel<RenameClassify>,
) -> Result<(), String> {
    let target = target.as_ref();
    log::info!("classify files {} to {}", src.len(), target.display());
    if !target.is_dir() {
        return Err(format!("{} is not a directory", target.display()));
    }

    // sub dir count
    let class_count = sub_dirs(target).unwrap_or(0);
    if class_count == 0 {
        // empty dir
        for src in src {
            channel
                .send(RenameClassify::Classify {
                    src,
                    target: target.to_string_lossy().to_string(),
                })
                .unwrap();
        }

        return Ok(());
    }

    let files = src
        .into_iter()
        // replace src to file rename target path
        .filter_map(|p| {
            let path = PathBuf::from(p);
            if path.is_file() && path.is_absolute() {
                Some(path)
            } else {
                None
            }
        })
        .collect::<Vec<_>>();

    if files.is_empty() {
        log::info!("no exits files, just skip");
        return Ok(());
    }

    let wisfile = wisfile.clone();

    let stream = wisfile
        .find_best_dir_stream(&files, target.to_path_buf())
        .await
        .map_err(|err| err.to_string())?;

    let mut stream = Box::pin(stream);
    // log::error!("{}", stream.is_err());

    log::error!("recv classify");
    while let Some(classify) = stream.next().await {
        log::error!("rec {classify:?}");
        send_classify_data(classify, channel);
    }

    let file_count = files.len();

    tauri::async_runtime::spawn(async move {
        log::info!(
            "start send file classify event count: {file_count}, class count: {class_count}"
        );
        let event = WisFileEvent::FileClassify {
            count: file_count,
            class_count,
        };

        if let Err(err) = event.send(device_sn()).await {
            log::error!("send classify event error. {err}");
        }
    });

    Ok(())
}

fn send_classify_data(
    classify: Result<ClassifyItem, (String, String)>,
    channel: &Channel<RenameClassify>,
) {
    match classify {
        Ok(classify_item) => match classify_item {
            ClassifyItem::Process(finished, total) => {
                log::info!("Classify Process: {finished}/{total}");
                channel
                    .send(RenameClassify::ClassifyProcess {
                        src: finished,
                        total,
                    })
                    .unwrap();
            }
            ClassifyItem::Classify(src, target) => {
                channel
                    .send(RenameClassify::Classify { src, target })
                    .unwrap();
            }
        },
        Err((src, err)) => {
            // todo
            channel
                .send(RenameClassify::ClassifyError { src, err_msg: err })
                .unwrap();
        }
    }
}

fn sub_dirs<P: AsRef<Path>>(path: P) -> std::io::Result<usize> {
    let entries = fs::read_dir(path)?;
    let count = entries
        .filter_map(|entry| {
            let entry = entry.ok()?;
            let path = entry.path();
            if path.is_dir() {
                let file_name = path.file_name()?.to_string_lossy();
                if file_name != "." && file_name != ".." {
                    return Some(());
                }
            }
            None
        })
        .count();

    Ok(count)
}
