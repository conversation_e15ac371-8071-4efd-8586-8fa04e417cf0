use http_body_util::BodyExt as _;
use tower_service::Service as _;

use crate::state::AppState;

use super::{build_axum_request, HttpRequest};

#[tauri::command]
pub async fn services(
    request: HttpRequest<String>,
    app: tauri::State<'_, AppState>,
) -> Result<String, String> {
    let request = build_axum_request(request).map_err(|err| {
        log::error!("build service request error. {err}");
        err
    })?;

    let response = {
        let mut app = app.service_manager.0.lock().await;
        app.call(request).await.map_err(|err| {
            log::error!("call service manager error. {err}");
            err.to_string()
        })?
    };

    if !response.status().is_success() {
        let body = response
            .into_body()
            .collect()
            .await
            .unwrap()
            .to_bytes()
            .to_vec();
        return Err(String::from_utf8(body).unwrap());
    }

    let body = response
        .into_body()
        .collect()
        .await
        .unwrap()
        .to_bytes()
        .to_vec();

    Ok(String::from_utf8(body).unwrap())
}
