use tauri::Manager;

use crate::llama::{<PERSON>lamaChild, LlamaMode};

#[tauri::command]
pub async fn embedding_alive(
    llama_mode: <PERSON>lamaMode,
    app_handle: tauri::AppHandle,
) -> Result<bool, String> {
    let Some(llama_child) = app_handle.try_state::<LlamaChild>() else {
        log::info!("llama child is not init yet");
        return Ok(false);
    };

    let is_alive = llama_child.is_alive(llama_mode).await;

    log::info!("llama {llama_mode:?} state {is_alive}");

    Ok(is_alive)
}
