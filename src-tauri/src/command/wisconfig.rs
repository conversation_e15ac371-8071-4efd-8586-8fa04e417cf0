use std::sync::atomic::Ordering;

use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use util::telemetry::FLY_MODE;

use crate::config::{WisConfig, WisConfigInner};

#[tauri::command]
pub async fn get_wis_config(app: AppHandle) -> Result<WisConfigInner, String> {
    let config = app
        .try_state::<WisConfig>()
        .ok_or_else(|| "Failed to get config".to_string())?;

    let config = config.0.lock().await.clone();
    log::info!("get wis config {config:?}");

    Ok(config)
}

#[tauri::command]
pub async fn update_wis_config(new_config: WisConfigInner, app: AppHandle) -> Result<(), String> {
    log::info!("update wis config {new_config:?}");
    FLY_MODE.fetch_and(new_config.fly_mode, Ordering::SeqCst);

    WisConfigInner::update_config(&app, move |old_config| {
        *old_config = new_config;
    })
    .await
    .map_err(|err| err.to_string())?;

    Ok(())
}
