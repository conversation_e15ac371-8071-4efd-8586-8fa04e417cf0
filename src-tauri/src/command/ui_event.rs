use util::{
    device::device_sn,
    telemetry::event::{TelemetrySend, WisFileEvent},
};

#[tauri::command]
pub async fn ui_event(event: String, props: serde_json::Value) -> Result<(), String> {
    log::info!(
        "ui event event: {event}, props: {}",
        serde_json::to_string(&props).unwrap()
    );
    let ui_event = WisFileEvent::UiEvent { event, props };

    ui_event.send(device_sn()).await.map_err(|err| {
        log::error!("upload ui event error, {err}");
        err.to_string()
    })?;

    Ok(())
}
