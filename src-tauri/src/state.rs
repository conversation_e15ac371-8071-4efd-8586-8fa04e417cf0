use std::sync::Arc;

use serde::{Deserialize, Serialize};
use service_manager::{
    api::state::{AppState as SMState, ManageState, ProcessState},
    wis_archive::models::model_file::ModelFile,
};
use tauri_plugin_store::StoreExt;
use wisfile_backend::state::AppState as WisFileState;

use tauri::{App, App<PERSON><PERSON><PERSON>, Manager as _};
use tokio::sync::{Mutex, Notify};
use wisfile_backend::axum::{Extension, Router};

use crate::llama::start_llama;

/// backend service state
pub struct AppState {
    pub service_manager: (Mutex<Router>, SMState),
    pub wisfile: (Mutex<Router>, WisFileState),
}

/// model state
pub struct ModelState(pub Mutex<SelectedModel>);

#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Clone)]
pub struct SelectedModel {
    pub rename: ModelType,
    pub embedding: ModelType,
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Clone)]
#[serde(tag = "type", content = "data")]
pub enum ModelType {
    Local(ModelFile),
    Online,
}

impl ModelType {
    pub fn is_online(&self) -> bool {
        match self {
            ModelType::Local(_) => false,
            ModelType::Online => true,
        }
    }
}

impl Default for SelectedModel {
    fn default() -> Self {
        SelectedModel {
            rename: ModelType::Online,
            embedding: ModelType::Online,
        }
    }
}

impl SelectedModel {
    pub fn load(
        app: &App,
        installed_models: &[ModelFile],
    ) -> Result<Self, tauri_plugin_store::Error> {
        let store = app.store("selected_model.json")?;

        let selected_models = store.get("selected_models");
        let mut selected_model = selected_models
            .and_then(|value| serde_json::from_value::<SelectedModel>(value).ok())
            .unwrap_or(SelectedModel::default());
        log::info!("get selected models {selected_model:?}");

        // filter unknown models
        selected_model.filter(installed_models);

        log::info!("load selected models {selected_model:?}");

        Ok(selected_model)
    }

    // filter unknown models
    fn filter(&mut self, installed_models: &[ModelFile]) {
        fn find_known_model(name: &str, installed_models: &[ModelFile]) -> Option<ModelFile> {
            installed_models
                .iter()
                .find(|model| model.name == name)
                .cloned()
        }

        if let ModelType::Local(ref model_file) = self.embedding {
            if let Some(model_file) = find_known_model(&model_file.name, installed_models) {
                self.embedding = ModelType::Local(model_file);
            } else {
                self.embedding = ModelType::Online;
            }
        }

        if let ModelType::Local(ref model_file) = self.rename {
            if let Some(model_file) = find_known_model(&model_file.name, installed_models) {
                self.rename = ModelType::Local(model_file);
            } else {
                self.rename = ModelType::Online;
            }
        }
    }

    pub fn store(&self, app: &AppHandle) -> Result<(), tauri_plugin_store::Error> {
        log::info!("store selected models {self:?}");

        let value = serde_json::to_value(self).unwrap();
        let store = app.store("selected_model.json")?;
        store.set("selected_models", value);

        Ok(())
    }

    pub fn models(&self) -> Vec<ModelFile> {
        let mut result = Vec::new();

        if let ModelType::Local(model) = &self.rename {
            result.push(model.clone());
        }

        if let ModelType::Local(model) = &self.embedding {
            result.push(model.clone());
        }

        result
    }

    pub fn is_rename_online(&self) -> bool {
        self.rename.is_online()
    }

    pub fn is_embedding_online(&self) -> bool {
        self.embedding.is_online()
    }
}

impl AppState {
    pub async fn new(app: &App) -> Self {
        // init wisdoc tools
        log::info!("init wisdoc tools");
        wisdoc_emb::inti_wisdoc_tool()
            .map_err(|err| log::error!("init wisdoc tools error. {err}"))
            .unwrap();

        let manage_state = ManageState::new();
        let installed_models = manage_state.installed_models.clone();
        let axum_state = SMState {
            wis: Arc::new(Mutex::new(manage_state)),
            process: Arc::new(Mutex::new(ProcessState::default())),
        };

        let shutdown_notify = Arc::new(Notify::new());
        // Create router with application state
        let service_manager: Router<()> = service_manager::api::create_app()
            .layer(Extension(shutdown_notify.clone()))
            .with_state(axum_state.clone());

        log::info!("init selected models");
        let selected_models = SelectedModel::load(app, &installed_models)
            .map_err(|err| log::error!("load selected models error. {err}"))
            .unwrap_or_default();

        let wisfile = wisfile_backend::create_app().await;

        let mut wisfile_state = WisFileState::new(!selected_models.is_embedding_online())
            .map_err(|err| {
                log::error!("create app state error, {err}");
                err
            })
            .unwrap();

        app.manage(ModelState(Mutex::new(selected_models)));
        log::info!("init selected done");

        // load state
        log::info!("load app state");
        wisfile_state
            .load()
            .await
            .map_err(|err| {
                log::error!("create app state error, {err}");
                err
            })
            .unwrap();

        // run state
        log::info!("run profile-sync");
        wisfile_state.clone().run();

        let wisfile = wisfile.with_state(wisfile_state.clone());

        Self {
            service_manager: (Mutex::new(service_manager), axum_state),
            wisfile: (Mutex::new(wisfile), wisfile_state),
        }
    }

    pub fn start_backend(app: &App) {
        tauri::async_runtime::block_on(async {
            let app_state = AppState::new(app).await;

            app.manage(app_state);

            log::info!("try to start llamacpp at start");
            if let Some(select_model) = app.try_state::<ModelState>() {
                let models = select_model.0.lock().await.clone();
                let _ = start_llama(app.handle(), models).await;
            }
        });
    }
}
