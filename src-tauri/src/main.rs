// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::{Arc, OnceLock};
use tauri::Manager;

use util::{
    device::device_sn,
    log::clean_old_log,
    telemetry::event::{TelemetrySend, WisFileEvent},
};
use wisfile_lib::{
    command::prelude::*,
    config::{set_app_version, WisConfig},
};

use tokio::sync::Mutex;
use wisfile_lib::{
    llama::LlamaChild,
    state::{AppState, ModelState},
    utils::*,
};

// Global AppHandle
pub static APP: OnceLock<tauri::AppHandle> = OnceLock::new();
pub static DEVICE_SN: OnceLock<tauri::AppHandle> = OnceLock::new();

fn main() {
    let (tx, rx) = tokio::sync::mpsc::channel(4);
    let rx = Arc::new(Mutex::new(rx));
    tauri::async_runtime::spawn(async move {
        tokio::select! {
            _ = tokio::signal::ctrl_c() => {
                log::info!("Received Ctrl+C, shutting down...");
            }
        }
        let _ = tx.send(()).await;
    });

    // only run in debug mode
    if cfg!(debug_assertions) {
        println!("running at dev mode, will kill all postgres process");
        let output = duct_sh::sh("kill -9 $(pgrep 'debug/llama-server')").read();
        println!("{output:?}");
    }

    // kill old process
    let output = duct_sh::sh("kill -9 $(pgrep -f com.yyzj.wisfile)").read();
    println!("{output:?}");

    tauri::Builder::default()
        .plugin(tauri_plugin_log::Builder::new().build())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_store::Builder::new().build())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_log::Builder::new().skip_logger().build())
        .invoke_handler(tauri::generate_handler![
            services,
            wisfile,
            embedding_alive,
            rename_and_classify,
            get_model,
            set_model,
            export_logs,
            file_rename,
            mv_file,
            create_collection,
            upload_file,
            ui_event,
            get_wis_config,
            update_wis_config,
            embedding_dirs
        ])
        .setup(|app| {
            APP.get_or_init(|| app.app_handle().clone());

            {
                // init log
                init_log(app, true);
                // set app version
                set_app_version(app.app_handle());
                // load config
                inti_env(app);
                // init config
                WisConfig::init(app.app_handle());
                // clean old log
                clean_old_log(app, 3);
                // start backend
                AppState::start_backend(app);
            }

            // send app open event
            tauri::async_runtime::spawn(async move {
                log::info!("start send app open event");
                let event = WisFileEvent::AppOpen;
                if let Err(err) = event.send(device_sn()).await {
                    log::error!("Failed to send app open event: {err}");
                };
            });

            Ok(())
        })
        .build(tauri::generate_context!())
        .expect("error while building tauri application")
        .run(move |app, event| match event {
            tauri::RunEvent::ExitRequested { .. } => {
                let app_handle = app.clone();
                tauri::async_runtime::block_on(async move {
                    shutdown(app_handle).await;
                    std::process::exit(0);
                });
            }
            tauri::RunEvent::Ready => {
                let app_handle = app.clone();
                let rx = rx.clone();
                tauri::async_runtime::spawn(async move {
                    rx.lock().await.recv().await;
                    shutdown(app_handle).await;
                    std::process::exit(0);
                });
            }
            tauri::RunEvent::Exit => {
                let app_handle = app.clone();
                tauri::async_runtime::block_on(async move {
                    shutdown(app_handle).await;
                    std::process::exit(0);
                });
            }
            _ => {}
        })
}

async fn shutdown(app: tauri::AppHandle) {
    {
        use wisfile_lib::config::WisConfig;

        if let Some(state) = app.try_state::<AppState>() {
            log::info!("Stopping services...");
            log::info!("Stopping service-manager");
            let _ = state.service_manager.1.wis.lock().await.shutdown().await;
            log::info!("Stopping save wisfile cache");
            let _ = state.wisfile.1.save_profile().await;
        }

        if let Some(state) = app.try_state::<ModelState>() {
            log::info!("Stopping save selected models");
            let selected_models = state.0.lock().await;
            let _ = selected_models.store(&app);
        }

        if let Some(state) = app.try_state::<WisConfig>() {
            log::info!("Stopping save app config");
            let selected_models = state.0.lock().await;
            let _ = selected_models.save(&app);
        }

        if let Some(state) = app.try_state::<LlamaChild>() {
            log::info!("Stopping llama");
            state.kill_all().await;
        }
    }

    tokio::time::sleep(std::time::Duration::from_millis(200)).await;
    log::info!("Shutdown completed");
}
