use std::{fs::OpenOptions, io::Write, ops::DerefMut};

use serde::{Deserialize, Serialize};
use tauri::Manager;
use tauri_plugin_shell::{
    process::{CommandChild, CommandEvent},
    ShellExt,
};
use tokio::sync::Mutex;
use util::path::app_log_dir_inner;

use crate::utils::*;

use crate::state::{AppState, SelectedModel};

#[derive(Debug, <PERSON>lone, Copy, Serialize, Deserialize)]
pub enum LlamaMode {
    Rename,
    Embedding,
}

impl LlamaMode {
    pub fn log_file(&self) -> &str {
        match self {
            LlamaMode::Rename => "llama-cpp-rename.log",
            LlamaMode::Embedding => "llama-cpp-embedding.log",
        }
    }
}

#[derive(Default)]
pub struct LlamaChild(pub Mutex<LlamaChildInner>);

#[derive(Default)]
pub struct LlamaChildInner {
    pub embedding: Option<CommandChild>,
    pub rename: Option<CommandChild>,
}

impl LlamaChild {
    async fn take_child(&self, mode: LlamaMode) -> Option<CommandChild> {
        let mut child = self.0.lock().await;
        match mode {
            LlamaMode::Rename => child.rename.take(),
            LlamaMode::Embedding => child.embedding.take(),
        }
    }

    async fn pid(&self, mode: LlamaMode) -> Option<u32> {
        let child = self.0.lock().await;
        match mode {
            LlamaMode::Rename => child.rename.as_ref().map(|child| child.pid()),
            LlamaMode::Embedding => child.embedding.as_ref().map(|child| child.pid()),
        }
    }

    pub async fn is_alive(&self, mode: LlamaMode) -> bool {
        let Some(pid) = self.pid(mode).await else {
            log::info!("llamacpp {mode:?} is not start");
            return true;
        };
        log::info!("llama {mode:?} pid {pid}");
        let system = sysinfo::System::new_all();
        system.process(sysinfo::Pid::from(pid as usize)).is_some()
    }

    pub async fn kill(&self, mode: LlamaMode) {
        log::info!("will kill old llama {mode:?}");
        if let Some(child) = self.take_child(mode).await {
            let _ = child.kill();
        }
    }

    pub async fn kill_all(&self) {
        log::info!("will kill all llama");
        self.kill(LlamaMode::Rename).await;
        self.kill(LlamaMode::Embedding).await
    }

    pub async fn set_child(&self, child: CommandChild, mode: LlamaMode) {
        let mut old_child = self.0.lock().await;
        let old_child = old_child.deref_mut();
        match mode {
            LlamaMode::Rename => {
                if let Some(old_child) = old_child.rename.take() {
                    log::info!("kill old rename llamacpp");
                    let _ = old_child.kill();
                }

                old_child.rename = Some(child)
            }
            LlamaMode::Embedding => {
                if let Some(old_child) = old_child.embedding.take() {
                    log::info!("kill old embedding llamacpp");
                    let _ = old_child.kill();
                }

                old_child.embedding = Some(child)
            }
        }
    }
}

async fn _start_llama(
    app: &tauri::AppHandle,
    model_name: &str,
    port: u16,
    mode: LlamaMode,
) -> Result<(), String> {
    // kill old llamacpp
    match app.try_state::<LlamaChild>() {
        Some(llama) => {
            log::info!("kill old llamacpp {mode:?}");
            llama.kill(mode).await;
        }
        None => {
            log::info!("init llama child");
            app.manage(LlamaChild::default());
        }
    }

    // get model path
    let model_path = {
        let Some(state) = app.try_state::<AppState>() else {
            return Err("app state is not init yet".to_string());
        };

        let state = state.service_manager.1.wis.lock().await;
        state.wis.model_path().join(model_name)
    };

    log::info!("model path {}", model_path.display());

    let mut args = vec![
        "-m".to_string(),
        model_path.to_string_lossy().to_string(),
        "--host".to_string(),
        "0.0.0.0".to_string(),
        "--port".to_string(),
        port.to_string(),
        "--parallel".to_string(),
        "4".to_string(),
        "--no-webui".to_string(),
    ];

    if matches!(mode, LlamaMode::Embedding) {
        args.push("--embeddings".to_string());
        args.push("-c".to_string());
        args.push("0".to_string());
        args.push("-ub".to_string());
        args.push("-4096".to_string());
    }

    if !cfg!(unix) {
        args.push("--device".to_string());
        args.push("none".to_string());
    }

    let mut llama_cpp = app.shell().sidecar("llama-server").unwrap().args(args);

    if let Ok(lib_path) = app.path().resource_dir().map(|dir| dir.join("libs")) {
        if cfg!(unix) {
            llama_cpp = llama_cpp.env("LD_LIBRARY_PATH", lib_path);
        } else {
            // TODO: FIXME
            llama_cpp = llama_cpp.current_dir(lib_path.join("x86-win"));
        }
    }

    let log_dir = app_log_dir_inner(app);
    let log_file = log_dir.join(mode.log_file());

    let (mut rx, child) = llama_cpp.spawn().expect("failed to start llamacpp");
    // set new child
    let llama_state = app.state::<LlamaChild>();
    llama_state.set_child(child, mode).await;

    tauri::async_runtime::spawn(async move {
        let log_file = log_file;

        let file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(log_file.as_path())
            .map_err(|err| log::error!("open {} file error. {}", log_file.display(), err));

        let Ok(mut file) = file else {
            log::warn!("will not record qdrant log");
            return;
        };

        // read events such as stdout
        while let Some(event) = rx.recv().await {
            match event {
                CommandEvent::Stderr(items) => {
                    let _ = file.write_all(&items);
                }
                CommandEvent::Stdout(items) => {
                    let _ = file.write_all(&items);
                }
                CommandEvent::Error(err) => {
                    let _ = file.write_all(err.as_bytes());
                }

                CommandEvent::Terminated(terminated_payload) => {
                    let _ = file.write_all(format!("{terminated_payload:?}").as_bytes());
                }
                _ => todo!(),
            }
        }
    });

    Ok(())
}

pub async fn kill_llama(app: &tauri::AppHandle, mode: LlamaMode) {
    log::info!("kill llama {mode:?}");
    if let Some(llama) = app.try_state::<LlamaChild>() {
        llama.kill(mode).await
    }
}

pub async fn start_llama(app: &tauri::AppHandle, model: SelectedModel) -> Result<(), String> {
    let model_type = model.embedding;

    match model_type {
        crate::state::ModelType::Local(model_file) => {
            log::info!("will switch or start embedding model");
            let model_path = format!(
                "{}{}{}",
                model_file.r#type.as_str(),
                std::path::MAIN_SEPARATOR,
                model_file.name
            );
            _start_llama(
                app,
                &model_path,
                *LLAMA_EMBEDDING_PORT,
                LlamaMode::Embedding,
            )
            .await?;
        }
        crate::state::ModelType::Online => {
            log::info!("will stop embedding model");
            kill_llama(app, LlamaMode::Embedding).await
        }
    }

    let model_type = model.rename;
    match model_type {
        crate::state::ModelType::Local(model_file) => {
            let model_path = format!(
                "{}{}{}",
                model_file.r#type.as_str(),
                std::path::MAIN_SEPARATOR,
                model_file.name
            );

            log::info!("will switch or start rename model");
            _start_llama(app, &model_path, *LLAMA_RENAME_PORT, LlamaMode::Rename).await?;
        }
        crate::state::ModelType::Online => {
            log::info!("will switch or start rename model");
            kill_llama(app, LlamaMode::Rename).await
        }
    }

    Ok(())
}
