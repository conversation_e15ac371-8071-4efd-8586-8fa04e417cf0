.tabs-bar-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.tabBar {
  display: flex;
  align-items: center;
  height: 42px;
  background: #ececf2;
  padding: 2px 12px;
  border: 1px solid #d2d2d8;
  position: relative;
  overflow: hidden;
}

.tabBar.windows {
  justify-content: space-between;
}

.windowControls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 12px;
  margin-left: 12px;
}

.mac.windowButton {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  padding: 0;
  cursor: pointer;
  position: relative;
}

/* 按钮伪元素基础样式 */
.mac.windowButton::before,
.mac.windowButton::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 12px;
  height: 12px;
  background-size: 12px 12px;
  background-repeat: no-repeat;
  background-position: center;
  transition: opacity 0.3s ease;
}

.windows.windowButton {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  border: none;
  padding: 0;
  cursor: pointer;
  position: relative;
}

/* 按钮伪元素基础样式 */
.windows.windowButton::before,
.windows.windowButton::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 18px;
  height: 18px;
  background-size: 18px 18px;
  background-repeat: no-repeat;
  background-position: center;
  transition: opacity 0.3s ease;
}

.mac.closeButton::before {
  background-image: url(../assets/close.svg);
  opacity: 1;
}

.mac.closeButton::after {
  background-image: url(../assets/close_hover.svg);
  opacity: 0;
}

.windowControls:hover .mac.closeButton::before {
  opacity: 0;
}

.windowControls:hover .mac.closeButton::after {
  opacity: 1;
}

/* 最小化按钮 */
.mac.minimizeButton::before {
  background-image: url(../assets/min.svg);
  opacity: 1;
}

.mac.minimizeButton::after {
  background-image: url(../assets/min_hover.svg);
  opacity: 0;
}

.mac.windowControls:hover .minimizeButton::before {
  opacity: 0;
}

.mac.windowControls:hover .minimizeButton::after {
  opacity: 1;
}

.mac.maximizeButton::before {
  background-image: url(../assets/full.svg);
  opacity: 1;
}

.mac.maximizeButton::after {
  background-image: url(../assets/full_hover.svg);
  opacity: 0;
}

.windowControls:hover .mac.maximizeButton::before {
  opacity: 0;
}

.windowControls:hover .mac.maximizeButton::after {
  opacity: 1;
}

.windows.closeButton::before {
  background-image: url(../assets/close_windows.svg);
  opacity: 1;
}
.windows.closeButton::after {
  background-image: url(../assets/close_windows_hover.svg);
  opacity: 0;
}
.windows.windowControls:hover .closeButton::before {
  opacity: 0;
}
.windows.windowControls:hover .closeButton::after {
  opacity: 1;
}
.windows.minimizeButton::before {
  background-image: url(../assets/min_windows.svg);
  opacity: 1;
}
.windows.minimizeButton::after {
  background-image: url(../assets/min_windows_hover.svg);
  opacity: 0;
}
.windows.windowControls:hover .minimizeButton::before {
  opacity: 0;
}
.windows.windowControls:hover .minimizeButton::after {
  opacity: 1;
}
.windows.maximizeButton::before {
  background-image: url(../assets/full_windows.svg);
  opacity: 1;
}
.windows.maximizeButton::after {
  background-image: url(../assets/full_windows_hover.svg);
  opacity: 0;
}
.windows.windowControls:hover .maximizeButton::before {
  opacity: 0;
}
.windows.windowControls:hover .maximizeButton::after {
  opacity: 1;
}

.tab {
  display: flex;
  align-items: center;
  border-radius: 8px;
  margin-right: 8px;
  padding: 4px 12px;
  background: #fff;
  color: #222;
  border: 1px solid #d2d2d8;
  box-shadow: 0 2px 8px #0001;
  cursor: grab;
  min-width: 80px;
  max-width: 220px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
}

.tab.inactive {
  background: #ececec;
  color: #888;
  font-weight: 400;
  border: 1px solid transparent;
  box-shadow: none;
}

.tab.dragging {
  opacity: 0;
  z-index: 2;
}

.closeTabButton {
  margin-left: 8px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  border: none;
  cursor: pointer;
  transition: all 0.15s ease;
  background-image: url(../assets/tab_close.svg);
  background-repeat: no-repeat;
  background-position: center;
}

.newTabButton {
  margin-left: 4px;
  width: 18px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-weight: 600;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.15s ease;
}

.dragOverlay {
  cursor: pointer;
  background: #fff;
  color: #222;
  border: 1px solid #d2d2d8;
  box-shadow: 0 2px 8px #0001;
  transform: scale(1.02);
  display: flex;
  align-items: center;
  min-width: 80px;
  max-width: 220px;
  padding: 4px 12px;
  border-radius: 8px;
  font-size: 14px;
}
