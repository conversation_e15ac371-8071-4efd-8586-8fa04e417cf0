export interface Tab {
  id: string;
  title: string;
  content?: React.ReactNode;
  path?: string;
  component?: React.ComponentType<any>;
  props?: Record<string, any>;
  isActive?: boolean;
  isPinned?: boolean;
  isClosable?: boolean;
  icon?: React.ReactNode;
  badge?: number | string;
}

export interface TabsBarProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "translate"> {
  tabs: Tab[];
  activeTabId: string;
  onClose?: () => void;
  onTabClick: (id: string) => void;
  onTabClose: (id: string) => void;
  onTabDragEnd: (fromId: string, toId: string) => void;
  onNewTab: () => void;
  className?: string;
  style?: React.CSSProperties;
  showNewTabButton?: boolean;
  supportOneTab?: boolean;
  newTabButtonText?: string;
  draggable?: boolean;
  enableWindowDrag?: boolean;
  translate?: (key: string) => string;
}
