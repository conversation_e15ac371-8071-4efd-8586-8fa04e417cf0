import React, { ReactNode } from "react";
import { TabsContext } from "./TabsContext";
import { useTabs } from "../hooks/useTabs";
import { Tab } from "../types";

interface TabsProviderProps {
  children: ReactNode;
  initialTabs?: Tab[];
}

export function TabsProvider({ children, initialTabs = [] }: TabsProviderProps) {
  const tabsContext = useTabs(initialTabs);

  return (
    <TabsContext.Provider value={tabsContext}>
      {children}
    </TabsContext.Provider>
  );
} 