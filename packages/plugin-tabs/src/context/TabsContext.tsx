import { createContext, useContext } from "react";
import { Tab } from "../types";

interface TabsContextType {
  defaultTab: Tab;
  tabs: Tab[];
  activeTabId: string;
  setActiveTabId: (id: string) => void;
  open: (tab: Tab) => void;
  removeTab: (id: string) => void;
  updateTab: (id: string, updates: Partial<Tab>) => void;
  reorderTabs: (fromId: string, toId: string) => void;
}

export const TabsContext = createContext<TabsContextType | undefined>(
  undefined
);

export function useTabsContext() {
  const context = useContext(TabsContext);
  if (context === undefined) {
    throw new Error("useTabsContext must be used within a TabsProvider");
  }
  return context;
}
