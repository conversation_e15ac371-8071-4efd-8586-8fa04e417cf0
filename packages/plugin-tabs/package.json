{"name": "@tauri-apps/plugin-tabs", "version": "1.0.0", "description": "A tab management plugin for Tauri applications", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc && npm run copy-styles", "copy-styles": "mkdir -p dist/components && cp src/components/*.css dist/components/ && cp -r src/assets/ dist/assets"}, "keywords": ["tauri", "plugin", "tabs", "react"], "author": "", "license": "MIT", "devDependencies": {"@types/react": "^19.1.4", "sass": "^1.72.0", "typescript": "^5.8.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tauri-apps/api": "^2.5.0"}, "dependencies": {"react-router-dom": "^7.6.1"}}