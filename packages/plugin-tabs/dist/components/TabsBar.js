import React, { useContext, useEffect, useLayoutEffect, useMemo, useRef, useState, } from "react";
import { DndContext, closestCenter, DragOverlay, useSensor, useSensors, PointerSensor, } from "@dnd-kit/core";
import { SortableContext, horizontalListSortingStrategy, useSortable, } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Window } from "@tauri-apps/api/window";
import "./TabsBar.css";
import { TabsContext } from "../context/TabsContext";
// 检测操作系统
const platform = navigator.platform.toLowerCase();
const isMac = platform.includes("mac");
const isWindows = platform.includes("win");
const isLinux = platform.includes("linux");
function WindowControls({ onClose }) {
    const appWindow = Window.getCurrent();
    const handleClose = async () => {
        if (onClose) {
            onClose();
            return;
        }
        await appWindow.close();
    };
    const handleMinimize = async () => {
        await appWindow.minimize();
    };
    const handleMaximize = async () => {
        if (await appWindow.isFullscreen()) {
            await appWindow.setFullscreen(false);
        }
        else {
            await appWindow.setFullscreen(true);
        }
    };
    return (React.createElement("div", { className: "windowControls", style: {
            "--gap": isMac ? "8px" : "4px",
            "--margin": isMac ? "12px" : "8px",
            "--radius": isMac ? "50%" : "0",
            "--font": isMac
                ? "-apple-system, BlinkMacSystemFont"
                : "Segoe UI, sans-serif",
            "--close-color": isMac ? "#ff5f56" : "#e81123",
            "--minimize-color": isMac ? "#ffbd2e" : "#e81123",
            "--maximize-color": isMac ? "#27c93f" : "#e81123",
        } },
        isMac && (React.createElement(React.Fragment, null,
            React.createElement("button", { className: "windowButton closeButton mac", onClick: handleClose }),
            React.createElement("button", { className: "windowButton minimizeButton mac", onClick: handleMinimize }),
            React.createElement("button", { className: "windowButton maximizeButton mac", onClick: handleMaximize }))),
        isWindows && (React.createElement(React.Fragment, null,
            React.createElement("button", { className: "windowButton minimizeButton windows", onClick: handleMinimize }),
            React.createElement("button", { className: "windowButton maximizeButton windows", onClick: handleMaximize }),
            React.createElement("button", { className: "windowButton closeButton windows", onClick: handleClose })))));
}
function SortableTab({ tab, active, onClick, onClose }) {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging, } = useSortable({ id: tab.id });
    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
    };
    const handleClick = (e) => {
        if (!isDragging) {
            e.stopPropagation();
            onClick();
        }
    };
    return (React.createElement("div", { ref: setNodeRef, style: style, ...attributes, ...listeners, onClick: handleClick, className: `tab ${!active ? "inactive" : ""} ${isDragging ? "dragging" : ""}` },
        tab.icon && React.createElement("span", { className: "tab-icon" }, tab.icon),
        React.createElement("span", { style: {
                flex: 1,
                minWidth: 0,
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                display: "block",
            } }, tab.title),
        tab.badge && React.createElement("span", { className: "tab-badge" }, tab.badge),
        (tab.isClosable !== false || active) && (React.createElement("div", { className: "closeTabButton", onClick: (e) => {
                e.stopPropagation();
                onClose();
            } }))));
}
export function TabsBar({ children, tabs, activeTabId, onClose, onTabClick, onTabClose, onTabDragEnd, onNewTab, className, style, showNewTabButton = true, newTabButtonText = "+", supportOneTab = true, translate, }) {
    const [activeTab, setActiveTab] = React.useState(null);
    const tabsBarContentRef = useRef(null);
    const [overflow, setOverflow] = useState(false);
    const tabsContext = useContext(TabsContext);
    useEffect(() => {
        console.log("TabsBar mounted with tabs:", tabsContext?.defaultTab);
    }, [tabsContext]);
    useLayoutEffect(() => {
        const checkOverflow = () => {
            const el = tabsBarContentRef.current;
            if (el) {
                setOverflow(el.scrollWidth > el.clientWidth);
            }
        };
        checkOverflow();
        window.addEventListener("resize", checkOverflow);
        return () => window.removeEventListener("resize", checkOverflow);
    }, [tabs.length]);
    const sensors = useSensors(useSensor(PointerSensor, {
        activationConstraint: {
            distance: 8,
        },
    }));
    const currentTabs = useMemo(() => {
        const defaultTab = tabsContext?.defaultTab;
        const newTabs = tabs.filter((tab) => supportOneTab
            ? true
            : tab.id !== defaultTab?.id && tab.path !== defaultTab?.path);
        return newTabs.slice().reverse();
    }, [tabsContext, tabs, activeTabId]);
    async function handleDragStart(event) {
        const { active } = event;
        setActiveTab(tabs.find((tab) => tab.id === active.id) || null);
    }
    function handleDragEnd(event) {
        const { active, over } = event;
        setActiveTab(null);
        if (over && active.id !== over.id) {
            onTabDragEnd(active.id, over.id);
        }
    }
    return (React.createElement("div", { className: "tabs-bar-container" },
        React.createElement(DndContext, { sensors: sensors, collisionDetection: closestCenter, onDragEnd: handleDragEnd, onDragStart: handleDragStart },
            React.createElement("div", { className: `tabBar ${className || ""} ${isMac ? "mac" : isWindows ? "windows" : "linux"}`, style: style, "data-tauri-drag-region": true },
                isMac && React.createElement(WindowControls, { onClose: onClose }),
                React.createElement("div", { ref: tabsBarContentRef, className: "tabs-bar-content", style: {
                        display: "flex",
                        overflow: "hidden",
                        flexDirection: "row-reverse",
                    } },
                    React.createElement(SortableContext, { items: currentTabs, strategy: horizontalListSortingStrategy }, currentTabs.map((tab) => (React.createElement(SortableTab, { key: tab.id, tab: {
                            ...tab,
                            title: translate ? translate(tab.title) : tab.title,
                            isClosable: !overflow,
                        }, active: tab.id === activeTabId, onClick: () => onTabClick(tab.id), onClose: () => onTabClose(tab.id) })))),
                    showNewTabButton && tabs.length != 0 && (React.createElement("div", { className: "newTabButton", onClick: onNewTab }, newTabButtonText))),
                isWindows && React.createElement(WindowControls, null)),
            React.createElement(DragOverlay, { dropAnimation: {
                    duration: 200,
                    easing: "cubic-bezier(0.18, 0.67, 0.6, 1.22)",
                } }, activeTab ? (React.createElement("div", { className: "dragOverlay" },
                activeTab.icon && (React.createElement("span", { className: "tab-icon" }, activeTab.icon)),
                React.createElement("span", { style: {
                        flex: 1,
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                    } }, translate ? translate(activeTab.title) : activeTab.title),
                activeTab.badge && (React.createElement("span", { className: "tab-badge" }, activeTab.badge)),
                activeTab.isClosable !== false && (React.createElement("button", { className: "closeTabButton" }, "\u00D7")))) : null)),
        children));
}
