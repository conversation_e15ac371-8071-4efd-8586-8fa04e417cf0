import { useState, useCallback, useEffect } from "react";
import { useNavigate } from "react-router-dom";
const STORAGE_TAB_KEY = "storage_saved_tabs";
const STORAGE_ACTIVE_KEY = "storage_active_tab";
export function useTabs(initialTabs = []) {
    const navigate = useNavigate();
    const defaultTab = initialTabs.length > 0
        ? initialTabs[0]
        : { id: "default", title: "Default Tab", path: "/" };
    const [tabs, setTabs] = useState(() => getInitialTabs(initialTabs));
    const [activeTabId, setActiveTabId] = useState(() => {
        const activeTabId = localStorage.getItem(STORAGE_ACTIVE_KEY);
        if (activeTabId) {
            return JSON.parse(activeTabId);
        }
        const savedTabs = localStorage.getItem(STORAGE_TAB_KEY);
        if (savedTabs) {
            const parsedTabs = JSON.parse(savedTabs);
            return parsedTabs[0]?.id || "";
        }
        return initialTabs[0]?.id || "";
    });
    useEffect(() => {
        {
            //TODO: 考虑是否放在组件内处理
            const path = tabs.find((tab) => tab.id === activeTabId)?.path;
            console.log("useTabs activeTabId", activeTabId, path, tabs);
            navigate(path || (defaultTab.path ?? "/"));
        }
        localStorage.setItem(STORAGE_ACTIVE_KEY, JSON.stringify(activeTabId));
    }, [activeTabId, defaultTab, tabs]);
    useEffect(() => {
        localStorage.setItem(STORAGE_TAB_KEY, JSON.stringify(tabs));
    }, [tabs]);
    function getInitialTabs(initialTabs) {
        try {
            const savedTabs = localStorage.getItem(STORAGE_TAB_KEY);
            if (savedTabs && savedTabs !== "[]") {
                const parsedTabs = JSON.parse(savedTabs);
                if (Array.isArray(parsedTabs) && parsedTabs.length > 0) {
                    return parsedTabs;
                }
            }
        }
        catch (error) {
            console.error("Error parsing saved tabs:", error);
        }
        return [defaultTab];
    }
    const open = useCallback((tab) => {
        setTabs((prev) => {
            const existingTab = prev.find((t) => t.id === tab.id && t.path === tab.path);
            if (existingTab) {
                setActiveTabId(existingTab.id);
                return prev;
            }
            return [...prev, tab];
        });
        setActiveTabId(tab.id);
    }, []);
    const removeTab = useCallback((id) => {
        setTabs((prev) => {
            const newTabs = prev.filter((tab) => tab.id !== id);
            if (activeTabId === id) {
                const lastTab = newTabs[newTabs.length - 1];
                if (lastTab) {
                    setActiveTabId(lastTab.id);
                }
                else {
                    setActiveTabId(defaultTab.id);
                }
            }
            return newTabs;
        });
    }, [activeTabId, defaultTab, navigate]);
    const updateTab = useCallback((id, updates) => {
        setTabs((prev) => prev.map((tab) => (tab.id === id ? { ...tab, ...updates } : tab)));
    }, []);
    const reorderTabs = useCallback((fromId, toId) => {
        setTabs((prev) => {
            const fromIndex = prev.findIndex((tab) => tab.id === fromId);
            const toIndex = prev.findIndex((tab) => tab.id === toId);
            if (fromIndex === -1 || toIndex === -1)
                return prev;
            const newTabs = [...prev];
            const [moved] = newTabs.splice(fromIndex, 1);
            newTabs.splice(toIndex, 0, moved);
            return newTabs;
        });
    }, []);
    return {
        defaultTab,
        tabs,
        activeTabId,
        setActiveTabId,
        open,
        removeTab,
        updateTab,
        reorderTabs,
    };
}
