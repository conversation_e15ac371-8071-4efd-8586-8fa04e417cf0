import { Tab } from "../types";
export declare function useTabs(initialTabs?: Tab[]): {
    defaultTab: Tab;
    tabs: Tab[];
    activeTabId: string;
    setActiveTabId: import("react").Dispatch<import("react").SetStateAction<string>>;
    open: (tab: Tab) => void;
    removeTab: (id: string) => void;
    updateTab: (id: string, updates: Partial<Tab>) => void;
    reorderTabs: (fromId: string, toId: string) => void;
};
