import { Tab } from "../types";
interface TabsContextType {
    defaultTab: Tab;
    tabs: Tab[];
    activeTabId: string;
    setActiveTabId: (id: string) => void;
    open: (tab: Tab) => void;
    removeTab: (id: string) => void;
    updateTab: (id: string, updates: Partial<Tab>) => void;
    reorderTabs: (fromId: string, toId: string) => void;
}
export declare const TabsContext: import("react").Context<TabsContextType | undefined>;
export declare function useTabsContext(): TabsContextType;
export {};
