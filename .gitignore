# Node.js
node_modules/
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
dist/
build/

# Vite
.vite/

# Tauri
/src-tauri/target/
/src-tauri/.cargo-lock
/src-tauri/Cargo.lock
/src-tauri/bundle/
/src-tauri/.tauri-build
/src-tauri/.DS_Store

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# IDEs
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# Logs
*.log

# Env files
.env
.env.*

# Optional: OS generated files
*.swp
*.bak
*.tmp

# Optional: coverage
coverage/

target/
/src-tauri/resources/wasmedge
/src-tauri/resources/llama-server-aarch64-apple-darwin
/src-tauri/resources/llama-server-x86_64-apple-darwin
/src-tauri/resources/llamacpp
.direnv
src-tauri/resources/llama-server-*
src-tauri/resources/llamacpp/
.history/
vendor/tauri-apps/*
package-lock.json
