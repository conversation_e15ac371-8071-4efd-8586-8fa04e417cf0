import React from "react";
import { useTabsContext } from "@tauri-apps/plugin-tabs";

const About: React.FC = () => {
  const { open } = useTabsContext();

  return (
    <div
      className="p-4"
      onClick={() => {
        const now = new Date();
        open({
          id: `new-tab${now.getTime()}`,
          title: "关于-标签",
          path: "/about",
        });
      }}
    >
      <h1 className="text-2xl font-bold mb-4">关于</h1>
      <p>这是关于页面</p>
    </div>
  );
};

export default About;
