import { useTabsContext } from "@tauri-apps/plugin-tabs";
import React from "react";

const Home: React.FC = () => {
  const { open } = useTabsContext();
  return (
    <div
      onClick={() => {
        const now = new Date();
        open({
          id: `new-tab${now.getTime()}`,
          title: "关于-标签",
          path: "/about",
        });
      }}
      className="p-4"
      style={{ height: "2000px" }}
    >
      <h1 className="text-2xl font-bold mb-4">首页</h1>
      <div className="mb-4">
        <p>欢迎来到首页！</p>
        <p>这里是一些示例内容。</p>
        <p>你可以在这里添加更多的内容。</p>
      </div>
    </div>
  );
};

export default Home;
