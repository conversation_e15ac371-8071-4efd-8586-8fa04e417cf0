import React, { useCallback, useEffect, useMemo } from "react";
import { TabsBar, useTabsContext, TabContent } from "@tauri-apps/plugin-tabs";
import { useNavigate, useLocation, Routes, Route } from "react-router-dom";
import "./App.css";
import Home from "./pages/Home";
import About from "./pages/About";
import Layout from "./layout";

const ROUTES: { path: string; content: React.ReactNode }[] = [
  {
    path: "/",
    content: <Home />,
  },
  {
    path: "/about",
    content: <About />,
  },
];

function App() {
  const navigate = useNavigate();

  const { tabs, activeTabId, setActiveTabId, open, removeTab, reorderTabs } =
    useTabsContext();

  // 标签切换
  const handleTabClick = useCallback(
    (tabId: string) => {
      setActiveTabId(tabId);
    },
    [tabs, navigate, setActiveTabId]
  );

  // 新建标签
  const handleNewTab = useCallback(() => {
    const newTab = {
      id: "/about",
      title: `新标签页 ${tabs.length + 1}`,
      path: "/about",
    };
    open(newTab);
  }, [tabs.length, open]);

  useEffect(() => {
    console.log("Tabs updated:", tabs.length, tabs);
  }, [tabs]);

  useEffect(() => {
    console.log("= Active tab changed:", activeTabId);
  }, [activeTabId]);

  // 渲染标签内容
  const renderTabContent = useMemo(() => {
    return (
      <Routes>
        {ROUTES.map((route) => {
          if (route.path == "/about") {
            return (
              <Route
                key={route.path}
                path={route.path}
                element={<TabContent />}
              >
                <Route element={<Layout />}>
                  <Route index element={route.content} />
                </Route>
              </Route>
            );
          }

          return (
            <Route key={route.path} path={route.path} element={<TabContent />}>
              <Route index element={route.content} />
            </Route>
          );
        })}
      </Routes>
    );
  }, []);

  return (
    <TabsBar
      tabs={tabs}
      activeTabId={activeTabId}
      onTabClick={handleTabClick}
      onTabClose={removeTab}
      onTabDragEnd={reorderTabs}
      onNewTab={handleNewTab}
      showNewTabButton={false}
    >
      {renderTabContent}
    </TabsBar>
  );
}

export default React.memo(App);
