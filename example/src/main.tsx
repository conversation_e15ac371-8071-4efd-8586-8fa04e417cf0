import React from "react";
import React<PERSON><PERSON> from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import App from "./App";
import "./index.css";
import { TabsProvider } from "@tauri-apps/plugin-tabs";

const initialTabs = [
  {
    id: "default",
    title: "测试",
    path: "/",
  },
];

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <BrowserRouter>
      <TabsProvider initialTabs={initialTabs}>
        <App />
      </TabsProvider>
    </BrowserRouter>
  </React.StrictMode>
);
