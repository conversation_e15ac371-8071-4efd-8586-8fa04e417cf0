{"name": "tabs", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@dnd-kit/sortable": "^10.0.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-store": "^2.2.0", "@tauri-apps/plugin-tabs": "file:../packages/plugin-tabs", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "vite": "^6.0.3"}}