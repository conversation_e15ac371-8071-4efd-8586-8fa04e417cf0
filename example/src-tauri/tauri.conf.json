{"$schema": "https://schema.tauri.app/config/2", "productName": "tabs", "version": "0.1.0", "identifier": "com.tabs.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1422", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"fullscreen": false, "resizable": true, "title": "Tabs Demo", "height": 600, "width": 800, "titleBarStyle": "Overlay", "hiddenTitle": true, "decorations": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}